package ru.portfolio.ax.rest.model;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import ru.portfolio.ax.configuration.JpaConfiguration;
import ru.portfolio.ax.configuration.datasource.aspect.ReadOnly;
import ru.portfolio.ax.configuration.datasource.aspect.WriteOnly;
import ru.portfolio.ax.model.*;
import ru.portfolio.ax.model.common.AbstractEntity;
import ru.portfolio.ax.model.common.Linkable;
import ru.portfolio.ax.model.common.PersonallyEntity;
import ru.portfolio.ax.model.dto.AttachmentDTO;
import ru.portfolio.ax.model.dto.PeriodDTO;
import ru.portfolio.ax.rest.AbstractController;
import ru.portfolio.ax.rest.api.PortfolioApi;
import ru.portfolio.ax.rest.api.PositiveResponse;
import ru.portfolio.ax.rest.dto.*;
import ru.portfolio.ax.rest.dto.aupd.AccessTokenPayloadDto;
import ru.portfolio.ax.rest.dto.aupd.CurrentUserRolesDTO;
import ru.portfolio.ax.rest.dto.contingent.SchoolGradeDTO;
import ru.portfolio.ax.rest.dto.nsi.NsiDTO;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.service.*;
import ru.portfolio.ax.service.ext.ContingentService;
import ru.portfolio.ax.service.ext.NsiService;
import ru.portfolio.ax.util.Utils;
import ru.portfolio.ax.util.aspect.LoggedMethod;
import ru.portfolio.ax.util.logging.ActionHistory;
import ru.portfolio.ax.util.security.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import ru.portfolio.ax.repository.UserRepository;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Objects;

import static java.util.stream.Collectors.*;
import static org.apache.commons.collections4.MapUtils.getLong;
import static org.apache.commons.collections4.MapUtils.getString;
import static org.springframework.data.domain.Sort.Direction.fromString;
import static ru.portfolio.ax.util.Utils.camel2underscore;
import static ru.portfolio.ax.util.Utils.safetyGet;

@Order(0)
@Configuration
@RestController
@RequiredArgsConstructor
public class EntityV170Controller {
    private static final String SELECT = "select id, person_id, creation_date, category_code, creator_id, '";
    private final ContingentService contingentService;
    private final JdbcTemplate jdbcTemplate;
    private final CrudService crudService;
    private final NsiService nsiService;
    private final DataService service;
    private final ClickHouseService clickHouseService;
    private final AuditService auditService;
    private final ExcelParserService excelParserService;
    private final AuthService authService;
    private final AuthComponent authComponent;
    private final AuthComponentNew authComponentNew;
    private final UserRepository userRepository;
    private final ObjectMapper objectMapper;
    @Value("${old.auth.enabled}")
    boolean oldAuthEnabled;

    @Value("${spring.datasource.hikari.schema}")
    private String schema;

    @Value("${aupd.subsystemId}")
    private String aupdSubsystem;
    @Value("${roles.global.adminId}")
    private String adminId;
    @Value("${roles.global.headTeacherId}")
    private String headTeacherId;
    @Value("${roles.global.schoolAdminId}")
    private String schoolAdminId;
    @Value("${roles.global.teacherId}")
    private String teacherId;
    @Value("${roles.global.operatorId}")
    private String operatorId;

    @ReadOnly
    @GetMapping("persons/{personId}/sort")
    //@Secured(globalRoles = Secured.GlobalRole.ALL)
    public PositiveResponse<Page<? extends AbstractEntity<Long>>> getPersonsObjects(@RequestHeader(name = "Authorization", required = false) String authorization,
                                                                                    @PathVariable String personId,
                                                                                    @RequestParam(required = false)
                                                                                    Set<Integer> categoryCode,
                                                                                    @RequestParam(defaultValue = "asc")
                                                                                    String sort,
                                                                                    @SortDefault(sort = "creationDate",
                                                                                            direction = Sort.Direction.DESC)
                                                                                    @PageableDefault Pageable pageable) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.standard(), personId, "getPersonsObjects", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.TEACHER,
                            SecuredNew.GlobalRole.STUDENT, SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.ADMIN)),
                    "getPersonsObjects", personId);
        }
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(authorization);
        HttpServletRequest httpRequest = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        Cookie aud = safetyGet(() ->
                Utils.first(Arrays.asList(httpRequest.getCookies()), c -> c.getName().equals("aupd_current_role")));
        PortfolioException.check(Objects.nonNull(aud),
                PortfolioException.get442("aupd_current_role"));
        tokenPayload.setAud(Utils.safeGet(aud, Cookie::getValue));
        Preconditions.checkNotNull(UUID.fromString(personId));

        String select = JpaConfiguration.classes.stream()
                .map(Class::getSimpleName)
                .map(t -> SELECT + t + "' as t from " +
                        schema + "." + camel2underscore(t) + " where is_delete = false")
                .collect(joining(" union all ",
                        "select * from (",
                        ") as vt "));
        select = select.replace("g_i_a", "gia");
        StringBuilder sb = new StringBuilder(select);
        sb.append(" where person_id = '")
                .append(personId).append('\'');

        if (Objects.nonNull(tokenPayload.getAud())) {
            String subsystem = tokenPayload.getAud().split(":")[0];
            String globalRole = tokenPayload.getAud().split(":")[1];

            if (subsystem.equals(aupdSubsystem)) {
                if (Lists.newArrayList(adminId, headTeacherId, schoolAdminId, teacherId, operatorId).contains(globalRole)) {
                    PortfolioException.check(Objects.nonNull(tokenPayload.getStf()), PortfolioException.get421());
                    sb.append(" and creator_id = '")
                            .append(tokenPayload.getStf()).append('\'');
                }
            }
        }

        if (CollectionUtils.isNotEmpty(categoryCode)) {
            sb.append(" and category_code in ");
            sb.append(categoryCode.stream().map(String::valueOf)
                    .collect(joining(", ", " (", ")")));
        }

        sb.append(" order by creation_date ")
                .append(fromString(sort).name().toLowerCase());

        String count = "select count(*) from ( " + sb + ") as vt ";
        sb.append(" offset ").append(pageable.getOffset());
        sb.append(" limit ").append(pageable.getPageSize());

        List<Map<String, Object>> maps = jdbcTemplate.queryForList(sb.toString());
        Long total = jdbcTemplate.queryForObject(count, Long.class);

        Map<String, Set<Long>> type2id = maps.stream().collect(
                Collectors.groupingBy(t -> getString(t, "t"),
                        mapping(t -> getLong(t, "id"), toSet())));

        Map<String, Map<Long, AbstractEntity<Long>>> class2id2entity = new LinkedHashMap<>();
        for (String clazz : type2id.keySet()) {
            List<AbstractEntity<Long>> list = crudService.findAll(clazz, type2id.get(clazz));
            list.forEach(o -> o.reachTransient(crudService));
            class2id2entity.put(clazz, Utils.index(list, AbstractEntity::getId));
        }

        List<AbstractEntity<Long>> result = new ArrayList<>(maps.size());
        for (Map<String, Object> map : maps) {
            result.add(class2id2entity
                    .get(getString(map, "t"))
                    .get(getLong(map, "id")));
        }

        clickHouseService.fillLinkedObject(
                result.stream().filter(x -> Linkable.class.isAssignableFrom(x.getClass()))
                .map(x -> (Linkable)x).collect(toList()));
        return PortfolioApi.positiveResponse(new PageImpl<>(result, pageable,
                ObjectUtils.defaultIfNull(total, NumberUtils.LONG_MINUS_ONE)));
    }

    @ReadOnly
    @LoggedMethod
    @PostMapping("persons/list")
    //@Secured(urlCookie = true, byPerson = false)
    public PositiveResponse<StudentsDTO> getPersonsList(@RequestHeader(name = "Authorization", required = false) String bearer,
                                                        @Valid @RequestBody PersonsSearchDTO personsSearchDTO) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.byCookieNotByPerson(), "getPersonsList", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.byCookieNotByPerson(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.TEACHER,
                            SecuredNew.GlobalRole.ADMIN)),
                    "getPersonsList", null);
        }
        StudentsDTO personList = service.getPersonList(bearer, personsSearchDTO.getCurrentSubsystemRoleId(),
                personsSearchDTO.getSearchParams(), personsSearchDTO.getSortParams());
        return PortfolioApi.positiveResponse(personList);
    }

    @WriteOnly
    //@Secured(byPerson = false)
    @PutMapping("employee/attachment")
    public PositiveResponse<String> putAttachment(@RequestHeader(name = "Authorization", required = false) String bearer,
                                                  @RequestParam Long entityId, @RequestParam String entityType,
                                                  @Valid @RequestBody AttachmentDTO.AttachmentsDTO attachments) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "putAttachment", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(Lists.newArrayList(SecuredNew.GlobalRole.TEACHER,
                            SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.ADMIN, SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.STUDENT))),
                    "putAttachment", null);
        }
        service.attachEntity(bearer, entityId, entityType, attachments);
        return PortfolioApi.positiveResponse("Ok");
    }

    @ReadOnly
    //@Secured(byPerson = false)
    @GetMapping("employee/parallels")
    public PositiveResponse<Collection<SchoolGradeDTO>> getEmployeeParallels(@RequestParam Long schoolId) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getEmployeeParallels", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.TEACHER,
                            SecuredNew.GlobalRole.ADMIN)),
                    "getEmployeeParallels", null);
        }
        return PortfolioApi.positiveResponse(contingentService.getSchoolGrade(schoolId));
    }

    //@Secured(byPerson = false)
    @GetMapping("employee/schools")
    public PositiveResponse<List<NsiDTO.Schools>> getEmployeeSchools() {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getEmployeeSchools", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.TEACHER,
                            SecuredNew.GlobalRole.ADMIN)),
                    "getEmployeeSchools", null);
        }
        return PortfolioApi.positiveResponse(Utils.transform(nsiService.getOrgNames(), NsiDTO.Schools::build));
    }

    //@Secured(byPerson = false)
    @PostMapping("action-history")
    public byte[] getActionHistoryReport(@RequestBody GetActionHistoryRequest request) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getActionHistoryReport", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.ADMIN)),
                    "getActionHistoryReport", null);
        }
        return excelParserService.getReport(request);
    }

    @WriteOnly
    //@Secured(byPerson = false)
    @SneakyThrows
    @GetMapping("/employee/templates")
    @ActionHistory(kindCode = 6)
    public byte[] getTemplates(@RequestHeader(name = "Authorization") @Nullable String bearer,
                               @RequestParam Integer categoryCode,
                               @RequestParam Integer dataKind,
                               @RequestParam @Nullable Integer typeCode) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getTemplates", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.TEACHER)),
                    "getTemplates", null);
        }
        return excelParserService.getTemplate(bearer, categoryCode, dataKind, typeCode);
    }

    @WriteOnly
    //@Secured(byPerson = false)
    @SneakyThrows
    @PostMapping("/admin/data-incorrect")
    public byte[] getDataIncorrect(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                   @RequestBody GetDataIncorrectRequestDTO request) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getDataIncorrect", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.ADMIN)),
                    "getDataIncorrect", null);
        }
        return excelParserService.getDataIncorrect(bearer, request);
    }

    @WriteOnly
    //@Secured(byPerson = false)
    @SneakyThrows
    @PostMapping("/employee/templates")
    @ActionHistory(kindCode = 7)
    public PositiveResponse<String> parseXSLFile(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                 @RequestPart(name = "attachment") ru.portfolio.ax.rest.dto.AttachmentDTO attachment,
                                                 @RequestPart(name = "file") @Nullable MultipartFile file) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "parseXSLFile", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.TEACHER)),
                    "parseXSLFile", null);
        }
        return PortfolioApi.positiveResponse(
                excelParserService.startImport(file, attachment, bearer));
    }

    @ReadOnly
    //@Secured(byPerson = false)
    @SneakyThrows
    @GetMapping("/employee/templates/report/{id}")
    public byte[] getReport(@RequestHeader(name = "Authorization") @Nullable String bearer,
                            @PathVariable Long id) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getReport", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.TEACHER)),
                    "getReport", null);
        }
        return excelParserService.getReport(id, bearer);
    }

    @WriteOnly
    //@Secured(byPerson = false)
    @SneakyThrows
    @DeleteMapping("/employee/templates")
    public PositiveResponse deleteImportInfo(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                             @RequestParam @Nullable String uuid) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "deleteImportInfo", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.TEACHER)),
                    "deleteImportInfo", null);
        }

        excelParserService.deleteImportInfo(uuid, bearer);
        return PortfolioApi.emptyPositiveResponse();
    }

    @ReadOnly
    //@Secured(byPerson = false)
    @SneakyThrows
    @GetMapping("/employee/templates/info")
    public PositiveResponse<AttachmentsDTO> getImportInfo(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                          @RequestParam String cacheUUID) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getImportInfo", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.TEACHER)),
                    "getImportInfo", null);
        }
        return PortfolioApi.positiveResponse(
                excelParserService.getImportInfo(cacheUUID, bearer));
    }

    @ReadOnly
    //@Secured(byPerson = false)
    @SneakyThrows
    @GetMapping("/employee/templates/info/{partitionNumber}")
    public PositiveResponse<List<AttachmentsDTO.Record>> getImportPartitionInfo(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                                                @RequestParam String cacheUUID,
                                                                                @PathVariable Integer partitionNumber) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getImportPartitionInfo", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.TEACHER)),
                    "getImportPartitionInfo", null);
        }
        return PortfolioApi.positiveResponse(
                excelParserService.getPartitionRecords(cacheUUID, partitionNumber, bearer));
    }

    @ReadOnly
    //@Secured(byPerson = false)
    @SneakyThrows
    @GetMapping("/employee/templates/list")
    public PositiveResponse<ImportListDTO> getImportList(@RequestHeader(name = "Authorization") @Nullable String bearer) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getImportList", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.OPERATOR,
                            SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.TEACHER)),
                    "getImportList", null);
        }
        return PortfolioApi.positiveResponse(excelParserService.getImportList(bearer));
    }

    @ReadOnly
    @SneakyThrows
    @GetMapping("/reference/teacher/{orgId}")
    public PositiveResponse<List<TeacherDTO>> getTeachers(@PathVariable Integer orgId,
                                                          @RequestParam(required = false) @Nullable String subject,
                                                          @RequestParam(required = false) @Nullable Integer limit) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getTeachers", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.OPERATOR, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.ADMIN,
                    SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.TEACHER)), "getTeachers", null);
        }

        // Проверка, что пользователь может искать учителей только в своей организации
        Integer userOrgId = getCurrentUserOrgId();
        PortfolioException.check(Objects.equals(orgId, userOrgId),
            PortfolioException.get420());

        return PortfolioApi.positiveResponse(service.getTeachers(orgId, subject, limit));
    }

    /**
     * Получение orgId текущего пользователя
     */
    private Integer getCurrentUserOrgId() {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        String authToken = StringUtils.substringAfter(request.getHeader("Authorization"), "Bearer ");

        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(authToken);
        User user = userRepository.findFirstByAupdId(tokenPayload.getSub()).orElse(null);
        PortfolioException.check(Objects.nonNull(user), PortfolioException.get469());

        CurrentUserRolesDTO currentUserRoles = objectMapper.convertValue(user.getCurrentUserRoles(), CurrentUserRolesDTO.class);

        // Получаем schoolId из currentSubsystemRoles
        if (Objects.nonNull(currentUserRoles.getCurrentSubsystemRoles()) && !currentUserRoles.getCurrentSubsystemRoles().isEmpty()) {
            String schoolId = currentUserRoles.getCurrentSubsystemRoles().get(0).getSchoolId();
            if (Objects.nonNull(schoolId)) {
                return Integer.valueOf(schoolId);
            }
        }

        // Если schoolId не найден в currentSubsystemRoles, пытаемся получить из currentSubsystemRole пользователя
        JsonNode currentSubsystemRole = user.getCurrentSubsystemRole();
        if (Objects.nonNull(currentSubsystemRole)) {
            UserContextDTO.CurrentGlobalRole.CurrentSubsystemRole subsystemRole =
                objectMapper.convertValue(currentSubsystemRole, UserContextDTO.CurrentGlobalRole.CurrentSubsystemRole.class);
            if (Objects.nonNull(subsystemRole.getOrganizationId())) {
                return subsystemRole.getOrganizationId().intValue();
            }
        }

        throw PortfolioException.get420();
    }

    @RestController
    @RequestMapping("affilations")
    public class AffilationController extends AbstractController<Affilation, Affilation> {
        public AffilationController(CrudService crudService, AuditService auditService, DataService dataService, AuthService authService) {
            super(Affilation.class, Affilation.class, crudService, auditService, dataService, authService);
        }
    }

    @RestController
    @RequestMapping("employments")
    public class EmploymentController extends AbstractController<Employment, Employment> {
        public EmploymentController(CrudService crudService, AuditService auditService, DataService dataService, AuthService authService) {
            super(Employment.class, Employment.class, crudService, auditService, dataService, authService);
        }
    }

    @RestController
    @RequestMapping("events")
    public class EventController extends AbstractController<Event, Event> {
        public EventController(CrudService crudService, AuditService auditService, DataService dataService, AuthService authService) {
            super(Event.class, Event.class, crudService, auditService, dataService, authService);
        }
    }

    @RestController
    @RequestMapping("projects")
    public class ProjectController extends AbstractController<Project, Project> {
        public ProjectController(CrudService crudService, AuditService auditService, DataService dataService, AuthService authService) {
            super(Project.class, Project.class, crudService, auditService, dataService, authService);
        }
    }

    @RestController
    @RequestMapping("rewards")
    public class RewardController extends AbstractController<Reward, Reward> {
        public RewardController(CrudService crudService, AuditService auditService, DataService dataService, AuthService authService) {
            super(Reward.class, Reward.class, crudService, auditService, dataService, authService);
        }
    }

    @RestController
    @RequestMapping("interests")
    public class InterestController extends AbstractController<Interest, Interest> {
        public InterestController(CrudService crudService, AuditService auditService, DataService dataService, AuthService authService) {
            super(Interest.class, Interest.class, crudService, auditService, dataService, authService);
        }
    }

    @RestController
    @RequestMapping("sport-rewards")
    public class SportRewardController extends AbstractController<SportReward, SportReward> {
        public SportRewardController(CrudService crudService, AuditService auditService, DataService dataService, AuthService authService) {
            super(SportReward.class, SportReward.class, crudService, auditService, dataService, authService);
        }
    }

    @RestController
    @RequestMapping("gia-worldskills")
    public class GIAWorldskillsController extends AbstractController<GIAWorldskills, GIAWorldskills> {
        public GIAWorldskillsController(CrudService crudService, AuditService auditService, DataService dataService, AuthService authService) {
            super(GIAWorldskills.class, GIAWorldskills.class, crudService, auditService, dataService, authService);
        }
    }

    @RestController
    @RequestMapping("document")
    public class DocumentController extends AbstractController<Document, Document> {
        public DocumentController(CrudService crudService, AuditService auditService, DataService dataService, AuthService authService) {
            super(Document.class, Document.class, crudService, auditService, dataService, authService);
        }
    }

    @RestController
    @RequestMapping("persons/professional-education/job")
    public class EducationJobController extends AbstractController<Job, Job> {
        public EducationJobController(CrudService crudService, AuditService auditService, DataService dataService, AuthService authService) {
            super(Job.class, Job.class, crudService, auditService, dataService, authService);
        }
    }

    @ReadOnly
    //@Secured(byPerson = false)
    @SneakyThrows
    @PostMapping("/employee/changeHistory")
    public PositiveResponse<Page<ChangeHistoryDTO>> getChangeHistory(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                                     @RequestBody GetChangeHistoryRequest request) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getChangeHistory", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.OPERATOR, SecuredNew.GlobalRole.ADMIN_O_O, SecuredNew.GlobalRole.ADMIN,
                    SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.TEACHER)), "getChangeHistory", null);
        }
        return PortfolioApi.positiveResponse(service.getChangeHistory(bearer, request));
    }

    @ReadOnly
    //@Secured(byPerson = false)
    @SneakyThrows
    @PostMapping("/employee/importHistory")
    public PositiveResponse<Page<ImportHistory>> getImportHistory(@RequestHeader(name = "Authorization") @Nullable String bearer,
                                                                  @RequestBody GetImportHistoryRequest request) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getImportHistory", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                            SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O,
                            SecuredNew.GlobalRole.ADMIN, SecuredNew.GlobalRole.TEACHER, SecuredNew.GlobalRole.OPERATOR)),
                    "getImportHistory", null);
        }
        return PortfolioApi.positiveResponse(service.getImportHistory(bearer, request));
    }

    @WriteOnly
    @ActionHistory(kindCode = 3)
    @DeleteMapping("/persons/delete/{entityType}/{id}")
    public <E extends PersonallyEntity> PositiveResponse<Page<E>> deletePersonallyEntity(@RequestHeader(name = "Authorization") String bearer,
                                                                                         @PathVariable String entityType,
                                                                                         @PathVariable Long id) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getImportHistory", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                            SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O,
                            SecuredNew.GlobalRole.ADMIN, SecuredNew.GlobalRole.TEACHER, SecuredNew.GlobalRole.OPERATOR)),
                    "getImportHistory", null);
        }
        return PortfolioApi.positiveResponse(auditService.changePersonallyEntityIsDelete(id, entityType, bearer, true));
    }

    @WriteOnly
    @ActionHistory(kindCode = 8)
    @PatchMapping("/persons/recover/{entityType}/{id}")
    public <E extends PersonallyEntity> PositiveResponse<Page<E>> recoverPersonallyEntity(@RequestHeader(name = "Authorization") String bearer,
                                                                                          @PathVariable String entityType,
                                                                                          @PathVariable Long id) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getImportHistory", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.notByPerson(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                            SecuredNew.GlobalRole.AGENT, SecuredNew.GlobalRole.HEAD_TEACHER, SecuredNew.GlobalRole.ADMIN_O_O,
                            SecuredNew.GlobalRole.ADMIN, SecuredNew.GlobalRole.TEACHER, SecuredNew.GlobalRole.OPERATOR)),
                    "getImportHistory", null);
        }
        return PortfolioApi.positiveResponse(auditService.changePersonallyEntityIsDelete(id, entityType, bearer, false));
    }

    @ReadOnly
    @PostMapping("/settings/entity/administrators/history")
    public PositiveResponse<Page<DeleteCardDTO>> getDeletedChangeHistory(@RequestBody GetActualDeletedChangeHistoryRequest request) {
        return PortfolioApi.positiveResponse(auditService.getActualDeletedChangeHistory(request));
    }

    @ReadOnly
    //@Secured(byPerson = false)
    @PostMapping("/error-message")
    public PositiveResponse<byte[]> getErrorMessageReport(@RequestBody PeriodDTO period,
                                                          @RequestHeader(name = "Authorization") String bearer) {
        if (oldAuthEnabled) {
            authComponent.userAuth(Secured.Secure.notByPerson(), "getErrorMessageReport", null);
        } else {
            authComponentNew.userAuth(SecuredNew.Secure.standard(Lists.newArrayList(SecuredNew.GlobalRole.STUDENT,
                    SecuredNew.GlobalRole.AGENT)), "getErrorMessageReport", null);
        }
        return PortfolioApi.positiveResponse(excelParserService.getErrorMessageReport(period.getStart(), period.getEnd(), bearer));
    }
}
