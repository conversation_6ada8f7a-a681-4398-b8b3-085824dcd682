package ru.portfolio.ax.util.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jwt.SignedJWT;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import ru.portfolio.ax.model.User;
import ru.portfolio.ax.model.common.PersonallyEntity;
import ru.portfolio.ax.repository.UserRepository;
import ru.portfolio.ax.rest.dto.aupd.AccessTokenPayloadDto;
import ru.portfolio.ax.rest.dto.aupd.AggregatedGlobalRole;
import ru.portfolio.ax.rest.dto.aupd.CurrentUserRolesDTO;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.service.CrudService;
import ru.portfolio.ax.service.ext.AUPDService;
import ru.portfolio.ax.util.Utils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static org.springframework.util.ObjectUtils.isEmpty;
import static ru.portfolio.ax.util.Utils.safetyGet;

@Service
@RequiredArgsConstructor
public class AuthService {
    private final ObjectMapper objectMapper;
    private final CrudService crudService;
    private final AUPDService aupdService;

    private final UserRepository userRepository;

    @Value("${aupd.issuer}")
    private String tokenIssuer;

    @Value("${aupd.certName}")
    private String certName;

    @Value("${aupd.subsystemId}")
    private Integer systemId;

    @Value("${roles.operator.adminOperator}")
    private Long adminLocalRole;
    @Value("${roles.global.childId}")
    private String childId;
    @Value("${roles.global.parentId}")
    private String parentId;
    @Value("${aupd.subsystemId}")
    private String employeeId;
    @Value("${roles.global.adminId}")
    private String adminId;
    @Value("${roles.global.operatorId}")
    private String operatorId;
    @Value("${roles.global.studentId}")
    private String studentId;

    @SneakyThrows
    public void checkValidToken(String token) {
        final byte[] keyByte = IOUtils.toByteArray(Objects.requireNonNull(getClass().getClassLoader().getResourceAsStream(certName)));
        final X509EncodedKeySpec pubKeySpec = new X509EncodedKeySpec(keyByte);
        final KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        final PublicKey publicKey = keyFactory.generatePublic(pubKeySpec);


        final JWSVerifier verifier = new RSASSAVerifier((RSAPublicKey) publicKey);
        try {
            final SignedJWT signedJWT = SignedJWT.parse(token);
            if (!signedJWT.verify(verifier)) {
                throw PortfolioException.get560();
            }
        } catch (Exception ex) {
            throw PortfolioException.get560();
        }
    }

    @SneakyThrows
    public AccessTokenPayloadDto getTokenPayload(String token) {
        return objectMapper.readValue(new String(Base64.getDecoder().decode(token.split("\\.")[1])), AccessTokenPayloadDto.class);
    }

    public List<AggregatedGlobalRole> parseRLS(String rls) {
        if (isEmpty(rls))
            return Collections.emptyList();


        final List<AggregatedGlobalRole> list = new ArrayList<>();

        AggregatedGlobalRole globalRole = null;
        AggregatedGlobalRole.AggregatedLocalRole localRole = null;

        final StringBuilder tmp = new StringBuilder(0);
        int mode = 0;
        int tmpId = 0;

        final int len = rls.length();

        for (int i = 0; i < len; i++) {
            final char c = rls.charAt(i);

            if (Character.isDigit(c))
                tmp.append(c);
            else {
                if (tmp.length() > 0) {
                    tmpId = Integer.parseInt(tmp.toString());
                    tmp.delete(0, tmp.length());
                }

                if (c == '{') {
                    globalRole = new AggregatedGlobalRole();
                    globalRole.localRoles = new ArrayList<>(0);
                    mode = 0;
                } else if (c == '}') {
                    if (globalRole != null) {
                        if (globalRole.id == 0 && tmpId > 0)
                            globalRole.id = tmpId;

                        final AggregatedGlobalRole gr = new AggregatedGlobalRole();
                        gr.id = globalRole.id;
                        gr.localRoles = globalRole.localRoles.stream().filter(x -> x.getSystemId() == systemId).collect(Collectors.toList());
                        list.add(gr);
                        globalRole = null;
                    }
                } else if (c == ':') {
                    if (globalRole != null && globalRole.id <= 0)
                        globalRole.id = tmpId;
                    else {
                        if (localRole.id == 0)
                            localRole.id = tmpId;
                        else
                            localRole.systemId = tmpId;
                    }

                    tmpId = 0;
                } else if (c == '[') {
                    mode++;

                    if (mode == 1) { // начало локальных ролей
                        localRole = new AggregatedGlobalRole.AggregatedLocalRole();
                        localRole.orgIds = new ArrayList<>(0);
                    }
                } else if (c == ']') {
                    mode--;
                    if (localRole != null) {
                        if (tmpId > 0) {
                            localRole.orgIds.add(tmpId);
                            tmpId = 0;
                        }

                        final AggregatedGlobalRole.AggregatedLocalRole lr = new AggregatedGlobalRole.AggregatedLocalRole();
                        lr.id = localRole.id;
                        lr.systemId = localRole.systemId;
                        lr.orgIds = new ArrayList<>(localRole.orgIds);
                        globalRole.localRoles.add(lr);

                        localRole = null;
                    }
                } else if (c == ',') {
                    if (mode == 2) {
                        localRole.orgIds.add(tmpId);
                        tmpId = 0;
                    } else if (localRole == null) {
                        localRole = new AggregatedGlobalRole.AggregatedLocalRole();
                        localRole.orgIds = new ArrayList<>(0);
                    }
                }
            }
        }

        return list.stream().filter(x -> !x.getLocalRoles().isEmpty()).collect(Collectors.toList());
    }

    public Integer getUserRoleFromToken(String bearer) {
        AccessTokenPayloadDto dto = getTokenPayload(bearer);
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        Cookie aud = safetyGet(() ->
                Utils.first(Arrays.asList(request.getCookies()), c -> c.getName().equals("aupd_current_role")));
        PortfolioException.check(Objects.nonNull(aud),
                PortfolioException.get442("aupd_current_role"));
        dto.setAud(Utils.safeGet(aud, Cookie::getValue));
        String globalId = dto.getAud().split(":").length == 2 ?
                dto.getAud().split(":")[1] : "0";
        if (globalId.equals(parentId)) return 11;
        if (globalId.equals(adminId)) return 14;
        if (globalId.equals(childId) || globalId.equals(studentId)) return 10;
        if (globalId.equals(operatorId)) return 12;
        if (globalId.equals(employeeId)) {
            List<AggregatedGlobalRole> roles = parseRLS(dto.getRls());
            for (AggregatedGlobalRole role : roles) {
                for (AggregatedGlobalRole.AggregatedLocalRole localRole : role.getLocalRoles()) {
                    if (Lists.newArrayList(32L, 33L, 34L, 35L, 36L, 37L, 38L).contains(localRole.getId())) {
                        return 12;
                    }
                    if (Lists.newArrayList(131L, 133L, 136L).contains(localRole.getId())) {
                        return 13;
                    }
                }
            }
        }
        return null;
    }

    public <E extends PersonallyEntity> void checkDelete(String bearer, Long id, Class<E> clazz, boolean isDelete) {
        E object = crudService.findPersonallyEntity(id, clazz);

        PortfolioException.check(Objects.nonNull(object.getSource())
                        && !object.getSource().getCode().equals(15),
                PortfolioException.get567("Недостаточно прав для редактирования данных (Источник: Платформа Учи.Ру)"));

        AccessTokenPayloadDto aupdToken = getTokenPayload(bearer);
        AggregatedGlobalRole aggregatedGlobalRole = parseRLS(aupdToken.getRls())
                .stream().filter(x -> Long.valueOf(adminId).equals(x.getId())).findFirst().orElse(null);
        if (!isDelete) {
            CurrentUserRolesDTO currentRole = aupdService.getCurrentUserRoles(bearer, aupdToken.getSub());
            if (!(currentRole.getCurrentMeshRoleId().equals(Long.valueOf(adminId))
                    && Objects.nonNull(aggregatedGlobalRole) && aggregatedGlobalRole.getLocalRoles()
                    .stream().map(AggregatedGlobalRole.AggregatedLocalRole::getId)
                    .anyMatch(x -> adminLocalRole.equals(x)))) throw PortfolioException.get562();
        } else if (!checkCreatorId(object, aupdToken)) {
            CurrentUserRolesDTO currentRole = aupdService.getCurrentUserRoles(bearer, aupdToken.getSub());
            if (!(currentRole.getCurrentMeshRoleId().equals(Long.valueOf(adminId))
                    && Objects.nonNull(aggregatedGlobalRole) && aggregatedGlobalRole.getLocalRoles()
                    .stream().map(AggregatedGlobalRole.AggregatedLocalRole::getId)
                    .anyMatch(x -> adminLocalRole.equals(x)))) throw PortfolioException.get481();
        }
    }

    public  <E extends PersonallyEntity> Boolean checkCreatorId(E object, AccessTokenPayloadDto aupdToken) {
        String creatorId;

        PortfolioException.check(Objects.nonNull(object.getSource())
                        && !object.getSource().getCode().equals(15),
                PortfolioException.get567("Недостаточно прав для редактирования данных (Источник: Платформа Учи.Ру)"));

        if (isNull(aupdToken.getStf())) {
            User user = userRepository.findFirstByAupdId(aupdToken.getSub()).orElseThrow(PortfolioException::get424);
            CurrentUserRolesDTO currentUserRolesDTO = objectMapper.convertValue(user.getCurrentUserRoles(),
                    CurrentUserRolesDTO.class);
            String personId = object.getPersonId();

            if (currentUserRolesDTO.getCurrentMeshRoleId().equals(Long.valueOf(parentId))) {
                if (!user.getChildren().contains(personId)) {
                    throw PortfolioException.get562();
                }
            } else if (currentUserRolesDTO.getCurrentMeshRoleId().equals(Long.valueOf(childId))) {
                if (!personId.equals(aupdToken.getMsh())) {
                    throw PortfolioException.get562();
                }
            }
            // проверка на то, что запись создана родителем или учеником
            return 10 == object.getSource().getCode() || 11 == object.getSource().getCode();
        } else {
            creatorId = aupdToken.getStf();
        }
        return object.getCreatorId().equals(creatorId);
    }

    public <E extends PersonallyEntity> void checkUpdate(String bearer, Long id, Class<E> clazz) {
        E object = crudService.find(id, clazz);
        AccessTokenPayloadDto aupdToken = getTokenPayload(bearer);
        if (!checkCreatorId(object, aupdToken)) throw PortfolioException.get481();
    }

    /**
     * Получение списка orgId текущего пользователя из токена
     */
    public List<Integer> getCurrentUserOrgIds(String authToken) {
        AccessTokenPayloadDto tokenPayload = getTokenPayload(authToken);

        // Получаем организации из RLS токена
        List<AggregatedGlobalRole> roles = parseRLS(tokenPayload.getRls());
        List<Integer> orgIds = new ArrayList<>();

        // Собираем orgIds из всех локальных ролей всех глобальных ролей
        for (AggregatedGlobalRole globalRole : roles) {
            for (AggregatedGlobalRole.AggregatedLocalRole localRole : globalRole.getLocalRoles()) {
                if (Objects.nonNull(localRole.getOrgIds())) {
                    orgIds.addAll(localRole.getOrgIds());
                }
            }
        }

        // Удаляем дубликаты
        orgIds = orgIds.stream().distinct().collect(Collectors.toList());

        PortfolioException.check(!orgIds.isEmpty(), PortfolioException.get562());
        return orgIds;
    }
}
