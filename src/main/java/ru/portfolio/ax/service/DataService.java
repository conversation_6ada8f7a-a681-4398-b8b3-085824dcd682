package ru.portfolio.ax.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.primitives.Longs;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Precision;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.util.CastUtils;
import org.springframework.data.util.Pair;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import ru.portfolio.ax.configuration.datasource.aspect.WriteOnly;
import ru.portfolio.ax.model.*;
import ru.portfolio.ax.model.common.AbstractEntity;
import ru.portfolio.ax.model.common.InterestRefEntity;
import ru.portfolio.ax.model.common.PersonallyEntity;
import ru.portfolio.ax.model.dto.AttachmentDTO;
import ru.portfolio.ax.model.dto.*;
import ru.portfolio.ax.model.enums.ActionType;
import ru.portfolio.ax.model.enums.GTOYearsCategory;
import ru.portfolio.ax.model.enums.InterestMap;
import ru.portfolio.ax.model.enums.PersonallyEntityEnum;
import ru.portfolio.ax.model.ref.*;
import ru.portfolio.ax.model.ref.interest.*;
import ru.portfolio.ax.repository.*;
import ru.portfolio.ax.repository.common.AttachmentProjection;
import ru.portfolio.ax.repository.ref.*;
import ru.portfolio.ax.repository.ref.interest.InterestActionKindRefRepository;
import ru.portfolio.ax.rest.dto.*;
import ru.portfolio.ax.rest.dto.StudentsDTO.StudentDTO;
import ru.portfolio.ax.rest.dto.aupd.AccessTokenPayloadDto;
import ru.portfolio.ax.rest.dto.aupd.AggregatedGlobalRole;
import ru.portfolio.ax.rest.dto.aupd.CurrentUserRolesDTO;
import ru.portfolio.ax.rest.dto.ceds.CEDSCreateDTO;
import ru.portfolio.ax.rest.dto.contingent.ClassDTO;
import ru.portfolio.ax.rest.dto.contingent.EducationDTO;
import ru.portfolio.ax.rest.dto.contingent.PersonDTO;
import ru.portfolio.ax.rest.dto.esz.CircleRequestDTO;
import ru.portfolio.ax.rest.dto.esz.CircleResponseDTO;
import ru.portfolio.ax.rest.dto.esz.SearchResultLimit;
import ru.portfolio.ax.rest.dto.library.LibraryResponseDTO;
import ru.portfolio.ax.rest.dto.nsi.NsiDTO;
import ru.portfolio.ax.rest.exception.PortfolioCodifiedEnum;
import ru.portfolio.ax.rest.exception.PortfolioException;
import ru.portfolio.ax.rest.kafka.GratitudeTeacherMessageDTO;
import ru.portfolio.ax.rest.kafka.KafkaProducer;
import ru.portfolio.ax.service.ext.*;
import ru.portfolio.ax.util.Utils;
import ru.portfolio.ax.util.security.AuthService;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.LongStream;
import java.util.stream.Stream;

import static java.util.Comparator.comparing;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static ru.portfolio.ax.model.PersonIdUpdate.buildDel;
import static ru.portfolio.ax.util.RestUtils.convertDirection;
import static ru.portfolio.ax.util.RestUtils.fillPageable;
import static ru.portfolio.ax.util.Utils.safetyGet;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataService {
    private final PersonUpdateRepository personUpdateRepository;
    private final ClickHouseRepository clickHouseRepository;
    private final ContingentService contingentService;
    private final PersonRepository personRepository;
    private final PersonAttachmentMetadataRepository personAttachmentMetadataRepository;
    private final TemplatesRefRepository templatesRefRepository;
    private final NsiService nsiService;
    private final EszService eszService;
    private final LibraryService libraryService;
    private final ClickHouseService clickHouseService;
    private final AuthService authService;
    private final CrudService crudService;
    private final CEDSService cedsService;
    private final S3Service s3Service;
    private final AuditService auditService;
    private final KafkaProducer kafkaProducer;
    private final ChangeHistoryRepository changeHistoryRepository;
    private final ImportHistoryRepository importHistoryRepository;
    private final LearnerHistoryRepository learnerHistoryRepository;
    private final GratitudeTeacherRepository gratitudeTeacherRepository;
    private final LearnerCategoryRefRepository learnerCategoryRefRepository;
    private final InterestActionKindRefRepository actionKindRefRepository;

    private final StopWordRepository stopWordRepository;

    private final UserRepository userRepository;
    private final AvatarSettingRepository avatarSettingRepository;

    private final CheckInHistoryRepository checkInHistoryRepository;
    private final CinemaRefRepository cinemaRefRepository;
    private final MuseumRefRepository museumRefRepository;
    private final TheatreRefRepository theatreRefRepository;

    private final SubjectsRefRepository subjectsRefRepository;
    private final RewardRepository rewardRepository;
    private final EventRepository eventRepository;
    private final ProjectRepository projectRepository;
    private final SportRewardRepository sportRewardRepository;
    private final AffRepository affilationRepository;
    private final EmplRepository employmentRepository;
    private final GIAWorldskillsRepository giaWorldskillsRepository;
    private final LinkedObjectRepository linkedObjectRepository;
    private final InterestRepository interestRepository;
    private final OrganizationMetroRepository organizationMetroRepository;
    private final InterestActionKindRefRepository interestActionKindRefRepository;
    private final ProffClassesRefRepository proffClassesRefRepository;
    private final DocumentRepository documentRepository;
    private final SpoStatusRepository spoStatusRepository;
    private final SpoStatusRefRepository spoStatusRefRepository;
    private final MetaskillRepository metaskillRepository;
    private final MetaskillRefRepository metaskillRefRepository;

    private final UserViewFunctionRepository userViewFunctionRepository;

    private final AdministratorErrorRecordRepository administratorErrorRecordRepository;
    private final JobRepository jobRepository;
    private final GiaExamRepository giaExamRepository;
    private final FavoriteUniversityRepository favoriteUniversityRepository;
    private final PersonalDataAccessRepository personalDataAccessRepository;

    private final MarkFinalRepository markFinalRepository;
    private final MarkAverageRepository markAverageRepository;

    private final PassedLessonRepository passedLessonRepository;
    private final SkippedLessonRepository skippedLessonRepository;

    private final SchoolRepository schoolRepository;
    private final ObjectMapper objectMapper;

    private final AUPDService aupdService;

    @Value("${roles.global.adminId}")
    private Long adminId;


    @Value("${mesh-contingent.newContingentCategoryId}")
    private Integer newContingentId;
    @Value("${mesh-contingent.nsi2CategoryId}")
    private Integer nsi1CategoryId;
    @Value("${mesh-contingent.nsi2CategoryId}")
    private Integer nsi2CategoryId;

    @Value("${roles.global.parentId}")
    private Long parentId;

    @Value("${roles.global.childId}")
    private Long childId;

    @Value("${roles.global.employeeId}")
    private Long employeeId;

    @Value("${roles.global.studentId}")
    private Long studentSpo;

    @Value("${roles.operator.adminOO}")
    private Long adminOO;

    @Value("${roles.operator.adminOperator}")
    private Long adminLocalRole;

    @Value("${roles.operator.employeeOO}")
    private Long employeeOO;

    @Value("${roles.operator.teacherOO}")
    private Long teacherOO;

    @Value("${attachments.ceds}")
    private Boolean attachmentsCeds;

    @Value("${context.actualize-persons}")
    private Boolean actualizePersons;

    private final Map<String, Integer> classCodeMap = Collections.unmodifiableMap(new HashMap<String, Integer>() {
        {
            put("UserProfilePhoto", 1);
            put("OlympiadParticipationCertificate", 2);
            put("StudentAchievement", 3);
        }
    });

    @SneakyThrows
    public byte[] getDocument(Long id, String storageId, String personId) {
        PortfolioException.check(nonNull(id) || nonNull(storageId), PortfolioException.get442("id, storageId"));
        PersonAttachmentMetadata attachmentMetadata = personAttachmentMetadataRepository.
                findByIdAndPersonIdAndCentralStorageDocumentIdAndDeleted(id, UUID.fromString(personId), storageId, false);
        PortfolioException.check(nonNull(attachmentMetadata), PortfolioException.get451());
        if (attachmentsCeds) {
            return cedsService.getDocument(attachmentMetadata.getCentralStorageDocumentId());
        } else {
            return s3Service.download(attachmentMetadata.getCentralStorageDocumentId());
        }
    }

    @WriteOnly
    @SneakyThrows
    public PersonAttachmentMetadata createNewDocument(MultipartFile document, String documentTitle, String documentClass, String fileFormat, String personId, String bearer) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        PortfolioException.check(document.getSize() < 20971520L, PortfolioException.get450());

        PortfolioException.check(Arrays.asList("application/pdf", "image/bmp", "image/jpeg", "image/jpg", "image/png")
                .contains(document.getContentType()), PortfolioException.get449());

        PortfolioException.check(classCodeMap.containsKey(documentClass), PortfolioException.get452());
        AttachmentFormatTypeRef formatTypeRef = crudService.findFirstRefByValue(AttachmentFormatTypeRef.class, document.getContentType().split("/")[1]);
        AttachmentTypeRef typeRef = crudService.findFirstRef(AttachmentTypeRef.class, classCodeMap.get(documentClass));

        String centralStorageDocumentId;
        if (attachmentsCeds) {
            CEDSCreateDTO newDocument = new CEDSCreateDTO();
            newDocument.setDocument(new String(Base64.getEncoder().encode(document.getBytes())));
            newDocument.setDocumentClass(documentClass);
            newDocument.setProperties(
                    CEDSCreateDTO.Properties.builder()
                            .documentTitle(documentTitle)
                            .ssoid(tokenPayload.getSso())
                            .mimetype(document.getContentType())
                            .build());
            centralStorageDocumentId = cedsService.create(newDocument);
        } else {
            centralStorageDocumentId = s3Service.upload(ObjectUtils.defaultIfNull(document.getOriginalFilename(), document.getName()),
                    document.getSize(), document.getInputStream(), document.getContentType());
        }

        PersonAttachmentMetadata pam = new PersonAttachmentMetadata();
        pam.setPersonId(UUID.fromString(personId));
        pam.setAttachmentType(typeRef);
        pam.setAttachmentFormatType(formatTypeRef);
        pam.setAttachingDatetime(ZonedDateTime.now());
        pam.setCentralStorageDocumentId(centralStorageDocumentId);
        pam.setSize(Math.toIntExact(document.getSize()));
        pam.setDeleted(false);
        pam.setCreationDate(LocalDateTime.now());
        pam.setModificationDate(LocalDateTime.now());
        pam.setName(documentTitle);
        personAttachmentMetadataRepository.save(pam);
        return pam;
    }


    public <T extends PersonallyEntity> void checkForSwear(T entry) {
        List<String> stopWords = stopWordRepository.findAll().stream().map(StopWordsRef::getWord).collect(Collectors.toList());
        Utils.checkForSwearInList(entry, stopWords);
    }

    public byte[] getTemplate(Integer categoryCode, Integer dataKind, Integer typeCode) {
        TemplatesRef template = templatesRefRepository.findByCategoryCodeAndDataKindAndTypeCode(categoryCode, dataKind, typeCode);
        PortfolioException.check(nonNull(template), PortfolioException.get432());
        return Base64.getDecoder().decode(template.getValue());
    }


    @SneakyThrows
    public StudentsDTO getPersonList(String bearer, Integer currentSubsystemRoleId, PersonsSearchParamsDTO searchParams, SortParamsDTO sortParams) {
        StudentsDTO students = new StudentsDTO();
//        if (currentSubsystemRoleId >= 32 && currentSubsystemRoleId <= 39) {

        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        AggregatedGlobalRole aggregatedGlobalRole = authService.parseRLS(tokenPayload.getRls())
                .stream().filter(x -> employeeId.equals(x.getId())).findFirst().orElse(null);
        boolean isOOEmployee = nonNull(aggregatedGlobalRole)
                && aggregatedGlobalRole.getLocalRoles().stream().noneMatch(x -> LongStream.range(32, 40)
                .boxed().collect(Collectors.toList()).contains(x.getId()))
                && aggregatedGlobalRole.getLocalRoles().stream()
                .anyMatch(x -> Lists.newArrayList(adminOO, employeeOO, teacherOO).contains(x.getId()));
        boolean isOOTeacher = isOOEmployee
                && aggregatedGlobalRole.getLocalRoles().stream()
                .noneMatch(x -> Lists.newArrayList(adminOO, employeeOO).contains(x.getId()))
                && aggregatedGlobalRole.getLocalRoles().stream()
                .anyMatch(x -> teacherOO.equals(x.getId()));

        if (isOOEmployee) {
            Set<String> schoolIds = aggregatedGlobalRole.getLocalRoles()
                    .stream().filter(x -> Objects.nonNull(x.getOrgIds()))
                    .map(AggregatedGlobalRole.AggregatedLocalRole::getOrgIds)
                    .flatMap(Collection::parallelStream)
                    .map(Object::toString)
                    .collect(Collectors.toSet());
            searchParams.setSchoolId(Lists.newArrayList(schoolIds));
        }
        if (isOOTeacher) {
            searchParams.setStaffId(tokenPayload.getStf());
            searchParams.setTrainingOn(Objects.isNull(searchParams.getTrainingOn()) ? LocalDate.now() : searchParams.getTrainingOn());
        }
        if (ObjectUtils.anyNotNull(searchParams.getSchoolId(), searchParams.getParallel(), searchParams.getGrade(), searchParams.getStaffId())) {
            searchParams.setTrainingOn(Objects.isNull(searchParams.getTrainingOn()) ? LocalDate.now() : searchParams.getTrainingOn());
        }

        PortfolioException.check(!Utils.allNull(searchParams), PortfolioException.get425());

        List<PersonDTO> persons = contingentService.getPersonsAll(searchParams);

        persons.forEach(x -> {
            StudentDTO student = new StudentDTO();
            student.setMshId(x.getPersonId().toString());
            student.setLastname(x.getLastname());
            student.setFirstname(x.getFirstname());
            student.setPatronymic(x.getPatronymic());
            student.setBirthdate(x.getBirthdate());
            Optional.ofNullable(x.getGender_id()).ifPresent(student::setGender);
            PortfolioException.check(nonNull(x.getEducation()), PortfolioException.get423());
            if (nonNull(x.getEducation()) && !x.getEducation().isEmpty()) {
                EducationDTO educationDTO = x.getEducation().stream()
                        .filter(ed1 -> nonNull(ed1.getBegin()) && nonNull(ed1.getEnd()))
                        .filter(ed2 -> !ed2.getBegin().isAfter(LocalDate.now()) && !ed2.getEnd().isBefore(LocalDate.now()))
                        .max(comparing(EducationDTO::getBegin)).orElse(null);
                if (nonNull(educationDTO) && nonNull(educationDTO.getClassDTO())) {
                    StudentsDTO.Grade grade = new StudentsDTO.Grade();
                    grade.setUid(educationDTO.getClassDTO().getUid());
                    grade.setName(educationDTO.getClassDTO().getName());
                    grade.setParallelName(educationDTO.getParallel());
                    student.setGrade(grade);
                    student.setOrganizationId(educationDTO.getOrganizationId());
                }
            }
            boolean isCorrectStudent = true;


            if (isOOTeacher) {
                EducationDTO educationDTO = x.getEducation().stream()
                        .filter(y -> nonNull(y.getParallelId()))
                        .max(comparing(EducationDTO::getParallelId)).orElse(null);

                if (Objects.nonNull(educationDTO) && Objects.nonNull(tokenPayload.getStf())
                        && !educationDTO.getStaffIds().contains(Integer.parseInt(tokenPayload.getStf()))) {
                    isCorrectStudent = false;
                }
            }
            if (nonNull(searchParams.getSchoolId())) {
                if (isNull(student.getOrganizationId()) || !searchParams.getSchoolId().contains(student.getOrganizationId().toString())) {
                    isCorrectStudent = false;
                }
            }
            if (nonNull(searchParams.getGrade())) {
                if (isNull(student.getGrade()) || !searchParams.getGrade().equals(RegExUtils.replaceAll(student.getGrade().getName(), "[^А-Яа-я]", ""))) {
                    isCorrectStudent = false;
                }
            }
            if (nonNull(searchParams.getParallel())) {
                if (isNull(student.getGrade()) || !searchParams.getParallel().equals(RegExUtils.replaceAll(student.getGrade().getName(), "\\D", ""))) {
                    isCorrectStudent = false;
                }
            }
            if (isCorrectStudent) {
                students.getStudent().add(student);
            }
        });
//        }
        if (nonNull(sortParams)) {
            Utils.sortList(students.getStudent(), sortParams.getSortField(), sortParams.getSort());
        }
        return students;
    }


    /**
     * Формирование информации о родителе/учащемуся в в зависимости от isParent
     *
     * @param person   Информация о персоне из контингента
     * @param isParent Флаг определяющий родителя/учащегося
     * @return
     */
    public PersonInfoDTO buildPersonInfo(PersonDTO person, Boolean isParent) {
        PersonInfoDTO personInfo = null;
        if (!isParent) {
            LocalDate localDateNow = LocalDate.now();
            // Получение обучения с максимальной датой начала
            Optional<EducationDTO> education = CollectionUtils.emptyIfNull(person.getEducation())
                    .stream()
                    .filter(ed1 -> nonNull(ed1.getBegin()) && nonNull(ed1.getEnd()))
                    .filter(ed2 -> !ed2.getBegin().isAfter(localDateNow) && !ed2.getEnd().isBefore(localDateNow))
                    .max(comparing(EducationDTO::getBegin));
            // Получение организации обучения
            Integer organizationId = education
                    .map(EducationDTO::getOrganizationId)
                    .orElse(null);
            // Получение класса обучения
            String uid = education
                    .map(EducationDTO::getClassUid)
                    .orElse(null);
            // Получение учителей обучения
            List<Integer> staffIds = education
                    .map(EducationDTO::getStaffIds)
                    .orElse(null);
            // Получение организации из НСИ (БД)
            String schoolName = null;
            String oktmo = null;
            NsiDTO.Response nsiInfo = nsiService.getOrgName(organizationId).orElse(null);
            if (nonNull(nsiInfo)) {
                schoolName = nsiInfo.getShortName();
                //oktmo = nsiInfo.getOktmo();
            }
            //temporary
            try {
                oktmo = Optional.ofNullable(organizationId)
                        .flatMap(nsiService::getOrgNameFromNsiService)
                        .map(NsiDTO.Response::getOktmo)
                        .orElse(null);
            } catch (Exception e) {

            }
            if (StringUtils.isNotBlank(oktmo) && oktmo.length() >= 5) {
                oktmo = oktmo.substring(0, 5) + "000";
            } else {
                oktmo = null;
            }
            // Формирование итоговых данных о учащемся
            personInfo = PersonInfoDTO
                    .builder()
                    .oldContingentId(person.getCategoryParameterValue(nsi1CategoryId))
                    .schoolName(schoolName)
                    .schoolId(organizationId)
                    .classId(uid)
                    .staffIds(staffIds)
                    .schoolOKTMO(oktmo)
                    .build();

        } else {
            // Получение идентификаторов детей родителя
            List<UUID> childrenIds = person.getChildren().stream().filter(x -> Objects.nonNull(x.getAgentPerson()))
                    .map(x -> x.getAgentPerson().getPersonId()).collect(Collectors.toList());
            personInfo = PersonInfoDTO
                    .builder()
                    .oldContingentId(person.getCategoryParameterValue(nsi1CategoryId))
                    .pupils(childrenIds)
                    .build();
        }

        // Заполнение основной информации о персоне
        Utils.copyNonNullProperties(person, personInfo);
        personInfo.setStudentData(true);
        personInfo.setGender(person.getGender_id());
        return personInfo;
    }

    @Transactional
    public void updateEducations(PersonDTO person) {
        if (Objects.isNull(person)) {
            return;
        }

        List<EducationDTO> educations = person.getEducation();
        String guid = person.getPersonId().toString();
        if (Objects.nonNull(educations) && !educations.isEmpty()) {
            LearnerHistory learnerHistory = learnerHistoryRepository.findAllByPersonId(guid).stream().findFirst().orElse(null);
            if (Objects.isNull(learnerHistory)) {
                learnerHistory = new LearnerHistory();
            }

            List<Long> globalIds = educations.stream()
                    .map(EducationDTO::getOrganizationId)
                    .map(Integer::longValue)
                    .collect(Collectors.toList());
            learnerHistory.setPersonId(guid);
            List<HistoryDTO> historyList = new ArrayList<>(educations.size());
            educations.forEach(education -> {
                NsiDTO.Response nsi = isNull(education.getOrganizationId()) ? null :
                        nsiService.getName(globalIds).orElse(null);
                HistoryDTO historyDTO = HistoryDTO.builder()
                        .periodEnd(education.getEnd())
                        .periodStart(education.getBegin())
                        .parallel(education.getParallel())
                        .classInfo(Utils.safeGet(education.getClassDTO(), ClassDTO::getName))
                        .id(education.getOrganizationId())
                        .eoId(Utils.safeGet(nsi, NsiDTO.Response::getEoId))
                        .name(Utils.safeGet(nsi, NsiDTO.Response::getShortName))
                        .build();
                historyList.add(historyDTO);
            });
            learnerHistory.setLearnerInfo(objectMapper.valueToTree(historyList));
            learnerHistoryRepository.save(learnerHistory);
        }
    }


    @Transactional
    public PersonInfoDTO getPersonInfo(String guid, String share, boolean updateEducations) {
        // 1.0
        PersonDTO person = Optional.ofNullable(safetyGet(() -> contingentService.getPerson(guid), e -> PortfolioException.is(e, PortfolioCodifiedEnum.E423)))
                // 1.1
                .orElseGet(() -> Utils.first(contingentService.getPersonsByFilter(guid)));
        PortfolioException.check(nonNull(person), new PortfolioException(PortfolioCodifiedEnum.E423));
        Preconditions.checkState(guid.equals(Objects.requireNonNull(person).getPersonId().toString()));


        // 2
        List<PersonDTO> childrenList = contingentService.getChildsByAgent(guid);
        List<UUID> personIds = Utils.transform(childrenList, PersonDTO::getPersonId);

        LocalDate localDateNow = LocalDate.now();

        Optional<EducationDTO> education = CollectionUtils.emptyIfNull(person.getEducation())
                .stream()
                .filter(y -> y.getServiceTypeId().equals(2))
                .max(comparing(EducationDTO::getBegin));
        // 4
        Integer organizationId = education
                .map(EducationDTO::getOrganizationId)
                .orElse(null);

        String uid = education
                .map(EducationDTO::getClassUid)
                .orElse(null);

        List<Integer> staffIds = education
                .map(EducationDTO::getStaffIds)
                .orElse(null);

        // 4.3
        String schoolName = null;
        String oktmo = null;
        NsiDTO.Response nsiInfo = nsiService.getOrgName(organizationId).orElse(null);
        if (nonNull(nsiInfo)) {
            schoolName = nsiInfo.getShortName();
            //oktmo = nsiInfo.getOktmo();
        }
        //temporary
        oktmo = Optional.ofNullable(organizationId)
                .flatMap(nsiService::getOrgNameFromNsiService)
                .map(NsiDTO.Response::getOktmo)
                .orElse(null);


        PersonInfoDTO personInfo = null;

        if (nonNull(share)) {
            ShareLink shareLink = clickHouseService.parseCookie(share);
            if (nonNull(shareLink) && !shareLink.getStudentData()) {
                personInfo = new PersonInfoDTO();
                personInfo.setLastname(person.getLastname());
                personInfo.setFirstname(person.getFirstname());
                personInfo.setPersonId(person.getPersonId());
                personInfo.setClassLevel(person.getClassLevel());
                personInfo.setGender(person.getGender_id());
                personInfo.setSchoolOKTMO(oktmo);
                personInfo.setStudentData(false);
            }
        }
        if (isNull(personInfo)) {
            personInfo = PersonInfoDTO
                    .builder()
                    .oldContingentId(person.getCategoryParameterValue(nsi1CategoryId))
                    .pupils(personIds)
                    .schoolName(schoolName)
                    .schoolId(organizationId)
                    .classId(uid)
                    .staffIds(staffIds)
                    .snils(person.getSnils())
                    .build();

            Utils.copyNonNullProperties(person, personInfo);
            personInfo.setStudentData(true);
            personInfo.setGender(person.getGender_id());
        }
        return personInfo;
    }

    public List<HistoryDTO> getHistoryInfo(String guid, String share) {

        if (nonNull(share)) {
            ShareLink shareLink = clickHouseService.parseCookie(share);
            //PortfolioException.check(Objects.isNull(shareLink) || shareLink.getEducationHistory(), PortfolioException.get461());
        }

        Collection<String> personIds = clickHouseService.getPersonIds(guid);

        List<PersonDTO> person;
        try {
            person = contingentService.getPersons(personIds);
        } catch (Exception ex) {
            return Collections.emptyList();
        }

        List<EducationDTO> educations = person
                .stream().map(PersonDTO::getEducation)
                .flatMap(List::stream).collect(Collectors.toList());
        List<HistoryDTO> historyList = new ArrayList<>(educations.size());

        List<Long> globalIds = educations.stream()
                .map(EducationDTO::getOrganizationId)
                .map(Integer::longValue)
                .collect(Collectors.toList());
        List<NsiDTO.Response> organizations = nsiService.getNames(globalIds);
        // 3
        educations.forEach(education -> {
            NsiDTO.Response nsi = isNull(education.getOrganizationId()) ? null :
                    organizations.stream()
                            .filter(x -> x.getGlobalId().equals(Long.valueOf(education.getOrganizationId())))
                            .findFirst().orElse(null);
            HistoryDTO historyDTO = HistoryDTO.builder()
                    .periodEnd(education.getEnd())
                    .periodStart(education.getBegin())
                    .id(education.getOrganizationId())
                    .eoId(Utils.safeGet(nsi, NsiDTO.Response::getEoId))
                    .name(Utils.safeGet(nsi, NsiDTO.Response::getShortName))
                    .build();
            // 4
//            Optional<ProgressDTO> progressOptional = clickHouseRepository.findAllBySchoolIdAndBetweenDateAndEventTypeByMaxEventDate(
//                    Utils.safeGet(nsi, NsiDTO.Response::getEoId), education.getBegin(), education.getEnd(), EventType.EDUCATION_PROFILE.getCode());
//            progressOptional.map(ProgressDTO::getProfile).ifPresent(historyDTO::setSpecialization);
//            Utils.copyNonNullProperties(education, historyDTO);
            historyList.add(historyDTO);
        });

        return historyList;
    }

    /**
     * Синхронизация данных по МЭШ.Контингент, добавление данных в personIdUpdate
     *
     * @param guid
     * @param nsi1FromMesh
     * @param nsi2FromMesh
     */
    @Async
    @Transactional
    public void actualizePersonIds(String guid, List<String> nsi1FromMesh, List<String> nsi2FromMesh) {
        System.out.println(Thread.currentThread().getName());
        List<String> existed = new ArrayList<>();
        //проверям, что полученных данных у нас ещё нет
        if (!nsi1FromMesh.isEmpty()) {
            for (String s : nsi1FromMesh) {
                if (personRepository.existsByMeshIdAndNsi1Id(guid, s)) {
                    existed.add(s);
                }
            }
        }
        nsi1FromMesh.removeAll(existed);
        existed.clear();
        if (!nsi2FromMesh.isEmpty()) {
            for (String s : nsi2FromMesh) {
                if (personRepository.existsByMeshIdAndNsi2Id(guid, s)) {
                    existed.add(s);
                }
            }
        }
        nsi2FromMesh.removeAll(existed);

        if (CollectionUtils.size(nsi1FromMesh) + CollectionUtils.size(nsi2FromMesh) == 0) {
            return;
        }
        // 1.0
        List<PersonId> personIdList = personRepository.findAllByMeshId(guid);
        List<PersonIdUpdate> nativeAllByMeshId = personUpdateRepository.findAllByMeshId(guid);

        Map<String, ActionType> nsi1Index = Utils.index(nativeAllByMeshId,
                PersonIdUpdate::getNsi1Id, PersonIdUpdate::getAction);
        Map<String, ActionType> nsi2Index = Utils.index(nativeAllByMeshId,
                PersonIdUpdate::getNsi2Id, PersonIdUpdate::getAction);

        // 1.1.1
        personIdList.removeIf(p ->
                ActionType.DELETE.equals(nsi1Index.remove(p.getNsi1Id())) ||
                        ActionType.DELETE.equals(nsi2Index.remove(p.getNsi2Id()))
        );
        // 1.1.2
        List<String> nsi1IdsFromDB = nsi1Index.entrySet().stream()
                .filter(e -> ActionType.ADD.equals(e.getValue()))
                .map(Map.Entry::getKey).filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<String> nsi2IdsFromDB = nsi2Index.entrySet().stream()
                .filter(e -> ActionType.ADD.equals(e.getValue()))
                .map(Map.Entry::getKey).filter(Objects::nonNull)
                .collect(Collectors.toList());

        nsi1IdsFromDB.addAll(Utils.extract(personIdList, PersonId::getNsi1Id));
        nsi2IdsFromDB.addAll(Utils.extract(personIdList, PersonId::getNsi2Id));

        Set<String> nsi1Mesh = Sets.newHashSet(nsi1FromMesh);
        Set<String> nsi2Mesh = Sets.newHashSet(nsi2FromMesh);

        // 3.2
        Collection<String> nsi1Intersection = CollectionUtils.intersection(nsi1IdsFromDB, nsi1Mesh);
        Collection<String> nsi2Intersection = CollectionUtils.intersection(nsi2IdsFromDB, nsi2Mesh);

        nsi1Mesh.removeAll(nsi1Intersection);
        nsi2Mesh.removeAll(nsi2Intersection);
        nsi1IdsFromDB.removeAll(nsi1Intersection);
        nsi2IdsFromDB.removeAll(nsi2Intersection);

        Set<String> shouldUpdateNs1 = Utils.extract(personIdList, PersonId::getNsi1Id,
                PersonId::getShouldUpdateFromMesh, StringUtils::isNoneEmpty, Collectors.toSet());
        Set<String> shouldUpdateNs2 = Utils.extract(personIdList, PersonId::getNsi2Id,
                PersonId::getShouldUpdateFromMesh, StringUtils::isNoneEmpty, Collectors.toSet());

        List<PersonIdUpdate> nsi1Add = Utils.transform(nsi1FromMesh, id ->
                PersonIdUpdate.buildAdd(guid, p -> p.setNsi1Id(id)));
        List<PersonIdUpdate> nsi2Add = Utils.transform(nsi2FromMesh, id ->
                PersonIdUpdate.buildAdd(guid, p -> p.setNsi2Id(id)));

        List<PersonIdUpdate> nsi1Del = Utils.transform(nsi1IdsFromDB,
                id -> buildDel(guid, p -> p.setNsi1Id(id)),
                p -> shouldUpdateNs1.contains(p.getNsi1Id()));
        List<PersonIdUpdate> nsi2Del = Utils.transform(nsi2IdsFromDB,
                id -> buildDel(guid, p -> p.setNsi2Id(id)),
                p -> shouldUpdateNs2.contains(p.getNsi2Id()));

        List<PersonIdUpdate> collect = Stream.of(nsi1Add, nsi2Add, nsi2Del, nsi1Del)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

        personUpdateRepository.saveAll(collect);
        collect.forEach(x -> updatePersonByAction(x, x.getAction()));
        //personUpdateRepository.updatePersonIdView();
    }

    /**
     * Разбор накопленных задач в personIdUpdate, обновление мат.вью
     */
    @Scheduled(cron = "#{cron.updatePersonId}")
    public void updatePersonIds() {
        List<PersonIdUpdate> nativeAll = personUpdateRepository.findAll();

        nativeAll.forEach(x -> updatePersonByAction(x, x.getAction()));

        personUpdateRepository.updatePersonIdView();
    }

    @Transactional
    public void updatePersonByAction(PersonIdUpdate personIdUpdate, ActionType actionType) {
        if (actionType.equals(ActionType.ADD)) {
            try {
                //пишем только новые данные
                if (!personRepository.existsByMeshIdAndNsi1Id(personIdUpdate.getMeshId(), personIdUpdate.getNsi1Id()) &&
                        !personRepository.existsByMeshIdAndNsi2Id(personIdUpdate.getMeshId(), personIdUpdate.getNsi2Id()))
                    personRepository.save(PersonId.buildFromUpdate(personIdUpdate));
                personUpdateRepository.delete(personIdUpdate);
            } catch (Exception ex) {
                log.error(ex.getMessage());
            }
        } else if (actionType.equals(ActionType.DELETE)) {
            try {
                if (nonNull(personIdUpdate.getNsi1Id())) {
                    personRepository.deleteByMeshIdAndNsi1Id(personIdUpdate.getMeshId(), personIdUpdate.getNsi1Id());
                } else {
                    personRepository.deleteByMeshIdAndNsi2Id(personIdUpdate.getMeshId(), personIdUpdate.getNsi2Id());
                }
                personUpdateRepository.delete(personIdUpdate);
            } catch (Exception ex) {
                log.error(ex.getMessage());
            }
        }
    }

    @WriteOnly
    @SneakyThrows
    @Transactional
    public void attachEntity(String bearer, Long entityId, String entityType, AttachmentDTO.AttachmentsDTO attachments) {
        if (CollectionUtils.isEmpty(attachments.getAttachment())) return;

        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);

        List<AbstractEntity<Long>> allById = crudService.findAll(entityType, Collections.singleton(entityId));

        AbstractEntity<Long> abstractEntity = allById.stream().findFirst().orElse(null);
        if (Objects.nonNull(abstractEntity) && PersonallyEntity.class.isAssignableFrom(abstractEntity.getClass())) {
            String personId = ((PersonallyEntity) abstractEntity).getPersonId();
            User user = userRepository.findFirstByAupdId(tokenPayload.getSub()).orElse(null);
            CurrentUserRolesDTO currentUserRolesDTO = objectMapper.convertValue(user.getCurrentUserRoles(), CurrentUserRolesDTO.class);
            if (currentUserRolesDTO.getCurrentMeshRoleId().equals(parentId)) {
                if (!user.getChildren().contains(personId)) {
                    throw PortfolioException.get562();
                }
            } else if (currentUserRolesDTO.getCurrentMeshRoleId().equals(childId)) {
                if (!personId.equals(tokenPayload.getMsh())) {
                    throw PortfolioException.get562();
                }
            }
        }

        Map<Long, AttachmentDTO> ids = Utils.index(attachments.getAttachment(), AttachmentDTO::getId);
        List<PersonAttachmentMetadata> attachmentMetadata = personAttachmentMetadataRepository.findAllById(ids.keySet());

        for (PersonAttachmentMetadata metadata : attachmentMetadata) {
            metadata.setEntityId(entityId);
            metadata.setEntityType(entityType);
            metadata.setDeleted(ids.get(metadata.getId()).getIsDelete());
        }
        personAttachmentMetadataRepository.saveAll(attachmentMetadata);
    }

    public Page<ChangeHistoryDTO> getChangeHistory(String bearer, GetChangeHistoryRequest request) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        User user = userRepository.findFirstByAupdId(tokenPayload.getSub()).orElse(null);
        PortfolioException.check(nonNull(user), PortfolioException.get424());
        List<Sort.Order> orders = new LinkedList<>();
        if (nonNull(request.getSort().getMethod())) {
            orders.add(new Sort.Order(convertDirection(request.getSort().getMethod()), "method"));
        }
        if (nonNull(request.getSort().getDate())) {
            orders.add(new Sort.Order(convertDirection(request.getSort().getDate()), "creationDate"));
        }
        Pageable pageable = fillPageable(request.getPagingOptions(), orders);
        LocalDateTime start = Utils.safeGet(request.getStartDate(), d -> LocalDateTime.of(d, LocalTime.MIN));
        LocalDateTime end = Utils.safeGet(request.getEndDate(), d -> LocalDateTime.of(d, LocalTime.MAX));
        Page<ChangeHistory> page = changeHistoryRepository.findByParams(user.getId(),
                request.getCategoryCode(), request.getMethod(), start, end, pageable);
        if (page.getTotalElements() == 0) {
            return Page.empty(pageable);
        }
        if (page.getTotalPages() < pageable.getPageNumber()) {
            request.getPagingOptions().setPageNumber(page.getTotalPages());
            pageable = fillPageable(request.getPagingOptions(), orders);
            page = changeHistoryRepository.findByParams(user.getId(),
                    request.getCategoryCode(), request.getMethod(), start, end, pageable);
        }
        List<ChangeHistoryDTO> responseContent = new ArrayList<>(page.getSize());
        page.forEach(changeHistory -> {
            PersonInfoDTO personInfo = getPersonInfo(changeHistory.getPersonId(), null, false);
            ChangeHistoryDTO changeHistoryDTO = new ChangeHistoryDTO();
            ChangeHistoryDTO.Student student = new ChangeHistoryDTO.Student();
            BeanUtils.copyProperties(changeHistory, changeHistoryDTO);
            changeHistoryDTO.setOldObject(changeHistory.getOldObject());
            student.setFirstName(personInfo.getFirstname());
            student.setLastName(personInfo.getLastname());
            student.setPatronymic(personInfo.getPatronymic());
            changeHistoryDTO.setStudent(student);
            responseContent.add(changeHistoryDTO);
        });
        return new PageImpl<>(responseContent, pageable, page.getTotalElements());
    }


    @Transactional
    public Page<ImportHistory> getImportHistory(String bearer, GetImportHistoryRequest request) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        List<Sort.Order> orders = new ArrayList<>();
        if (nonNull(request.getSort().getCorrectCount())) {
            orders.add(new Sort.Order(convertDirection(request.getSort().getCorrectCount()), "correctCount"));
        }
        if (nonNull(request.getSort().getDate())) {
            orders.add(new Sort.Order(convertDirection(request.getSort().getDate()), "creationDate"));
        }
        Pageable pageable = fillPageable(request.getPagingOptions(), orders);
        LocalDateTime start = null;
        if (nonNull(request.getStartDate())) start = LocalDateTime.of(request.getStartDate(), LocalTime.MIN);
        LocalDateTime end = null;
        if (nonNull(request.getEndDate())) end = LocalDateTime.of(request.getEndDate(), LocalTime.MAX);
        return importHistoryRepository.findByParams(tokenPayload.getStf(),
                request.getCategoryCode(), start, end, pageable);
    }

    public NearbyOrganizationsDTO getNearbyOrganizationList(Double geocodeX, Double geocodeY) {
        List<CinemaRef> cinemaInRadius = cinemaRefRepository.findAllInRadius(geocodeX, geocodeY, 0.0027);
        List<MuseumRef> museumInRadius = museumRefRepository.findAllInRadius(geocodeX, geocodeY, 0.01);
        List<TheatreRef> theatreInRadius = theatreRefRepository.findAllInRadius(geocodeX, geocodeY, 0.0027);

        NearbyOrganizationsDTO nearbyOrganizations = new NearbyOrganizationsDTO();
        nearbyOrganizations.getMuseum().addAll(
                museumInRadius.stream().map(MuseumRef::toOrganization).collect(Collectors.toList()));
        nearbyOrganizations.getTheatre().addAll(
                theatreInRadius.stream().map(TheatreRef::toOrganization).collect(Collectors.toList()));
        nearbyOrganizations.getCinema().addAll(
                cinemaInRadius.stream().map(CinemaRef::toOrganization).collect(Collectors.toList()));

        return nearbyOrganizations;
    }

    @Transactional
    public CheckInResponseDTO addNewCheckIn(String personId, NewCheckInDTO checkInDTO) {
        List<CheckInHistory> allCheckInByPerson = checkInHistoryRepository.findAllByPersonIdAndIsDelete(personId, false);
        if (allCheckInByPerson.stream().anyMatch(x -> x.getCreationDate().isAfter(LocalDateTime.now().minusHours(2)))) {
            return CheckInResponseDTO.builder()
                    .exceptionDescription("Упс, отметиться в одном месте можно только 1 раз за 2 часа").build();
        }
        if (allCheckInByPerson.stream().filter(x -> x.getCreationDate().isAfter(LocalDate.now().atStartOfDay())).count() >= 5) {
            return CheckInResponseDTO.builder()
                    .exceptionDescription("Упс, за сегодняшний день вы зачекинились уже в 5 местах. Попробуйте завтра.").build();
        }
        if (allCheckInByPerson.stream().anyMatch(x -> x.getCreationDate().isAfter(LocalDate.now().atStartOfDay())
                && ((Objects.nonNull(x.getCinema()) && x.getCinema().getCode().equals(checkInDTO.getInstitution().getId())) ||
                (Objects.nonNull(x.getMuseum()) && x.getMuseum().getCode().equals(checkInDTO.getInstitution().getId())) ||
                (Objects.nonNull(x.getTheatre()) && x.getTheatre().getCode().equals(checkInDTO.getInstitution().getId()))))) {
            return CheckInResponseDTO.builder()
                    .exceptionDescription("Упс, сегодня вы тут уже были. Попробуйте завтра.").build();
        }

        CheckInHistory checkIn = new CheckInHistory();
        checkIn.setPersonId(personId);
        checkIn.setCulturalEvent(crudService.findRef(checkInDTO.getInstitution().getType(), CulturalEventRef.class));
        checkIn.setOrganizer(checkInDTO.getOrganizer());
        checkIn.setEvent(checkInDTO.getEvent());
        checkIn.setDescription(checkInDTO.getDescription());
        checkIn.setDate(checkInDTO.getDate());
        checkIn.setCreationDate(ZonedDateTime.now().toLocalDateTime());
        checkIn.setCategory(crudService.findFirstRef(SectionRef.class, 5));
        checkIn.setTypeCode(48L);
        checkIn.setDataKind(47L);
        checkIn.setSource(crudService.find(10, DataSourceRef.class));
        checkIn.setFormat(crudService.find(1, OlympiadFormatRef.class));
        checkIn.setLevel(crudService.find(3, OlympiadTypeRef.class));
        checkIn.setIsDelete(false);

        Integer institutionId = checkInDTO.getInstitution().getId();
        switch (checkInDTO.getInstitution().getType()) {
            case 1:
                checkIn.setMuseum(crudService.find(institutionId, MuseumRef.class));
                break;
            case 2:
                checkIn.setTheatre(crudService.find(institutionId, TheatreRef.class));
                break;
            case 3:
                checkIn.setCinema(crudService.find(institutionId, CinemaRef.class));
                break;
            default:
                throw PortfolioException.get473();
        }
        CheckInHistory savedCheckIn = crudService.create(checkIn);

        Optional.ofNullable(checkInDTO.getAttachment()).ifPresent(attachmentsDTO -> {
            Optional.ofNullable(attachmentsDTO.getAttachment()).ifPresent(attachments -> {
                attachments.forEach(x -> {
                    Optional<PersonAttachmentMetadata> attachment = personAttachmentMetadataRepository.findById(x.getId());
                    attachment.ifPresent(y -> {
                        y.setDeleted(x.getIsDelete());
                        y.setEntityType("checkIn");
                        y.setEntityId(savedCheckIn.getId());
                        personAttachmentMetadataRepository.save(y);
                    });
                });
            });
        });

        return CheckInResponseDTO.builder()
                .institution(CheckInResponseDTO.Institution.builder()
                        .id(institutionId)
                        .type(checkInDTO.getInstitution().getType()).build())
                .build();
    }

    public List<CheckInHistoryDTO> getCheckInHistoryByPersonId(String personId, String share) {
        if (Objects.nonNull(share)) {
            ShareLink shareLink = clickHouseService.parseCookie(share);
            PortfolioException.check(shareLink.getOfflineVisit(), PortfolioException.get461());
        }
        List<CheckInHistoryDTO> response = new ArrayList<>();
        List<CheckInHistory> checkInHistoryList = checkInHistoryRepository.findAllByPersonIdAndIsDelete(personId, false);
        clickHouseService.fillLinkedObject(checkInHistoryList);
        checkInHistoryList.forEach(x -> {
            CheckInHistoryDTO checkInHistoryDTO = new CheckInHistoryDTO();
            checkInHistoryDTO.setCheckInHistory(x);
            List<AttachmentProjection> attachments
                    = personAttachmentMetadataRepository.findAllByEntityIdAndEntityTypeAndDeleted(x.getId(), "checkIn", false);
            checkInHistoryDTO.setAttachments(attachments);
            response.add(checkInHistoryDTO);
        });
        return response;
    }

    public List<ViewedFunction> getViewedFunctions(String bearer) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        if (Objects.nonNull(tokenPayload.getMsh())) {
            List<ViewedFunction> functions = new ArrayList<>();
            List<UserViewFunction> viewed = userViewFunctionRepository.findAllByPersonId(tokenPayload.getMsh());
            List<FunctionRef> allFunctions = crudService.findAllRefs(FunctionRef.class);
            List<Integer> viewedCodes = viewed.stream().map(x -> x.getFunction().getCode()).collect(Collectors.toList());
            allFunctions.forEach(x -> {
                ViewedFunction function = new ViewedFunction();
                function.setCode(x.getCode());
                function.setValue(x.getValue());
                function.setIsViewed(viewedCodes.contains(x.getCode()));
                function.setIsArchive(x.getIsArchive());
                functions.add(function);
            });
            return functions;
        } else {
            return new ArrayList<>();
        }
    }

    @WriteOnly
    @Transactional
    public UserViewFunction addUserViewedFunction(String bearer, Integer functionCode) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        PortfolioException.check(Objects.nonNull(tokenPayload.getMsh()) || Objects.nonNull(functionCode),
                PortfolioException.get442("personId, functionCode"));
        FunctionRef functionRef = crudService.find(functionCode, FunctionRef.class);
        PortfolioException.check(Objects.nonNull(functionRef), PortfolioException.get432());


        UserViewFunction uvf = new UserViewFunction();
        uvf.setPersonId(tokenPayload.getMsh());
        uvf.setFunction(functionRef);
        return userViewFunctionRepository.save(uvf);
    }

    public CertificatesDTO getCertificates(String personId, String bearer, String shareCookie) {
        if (StringUtils.isBlank(bearer) && Objects.nonNull(shareCookie)) {
            ShareLink shareLink = clickHouseService.parseCookie(shareCookie);
            PortfolioException.check(StringUtils.equals(Objects.requireNonNull(shareLink).getPersonId(),
                    personId), PortfolioException.get562());
        }
        Collection<String> personIds = clickHouseService.getPersonIds(personId);
        List<CertificatesDTO.Certificate> certificates = clickHouseRepository.searchCertificates(personIds, 17);

        CertificatesDTO certificatesDTO = new CertificatesDTO();
        certificatesDTO.setPersonId(personId);
        if (certificates.isEmpty()) {
            return certificatesDTO;
        } else {
            certificates.sort(comparing(CertificatesDTO.Certificate::getEventDate)
                    .thenComparing(CertificatesDTO.Certificate::getEventTime).reversed());
            certificatesDTO.getCertificate().addAll(certificates);
            return certificatesDTO;
        }
    }

    public List<LessonsDTO> getLessonsResult(String personId, String subjectName) {
        Collection<String> personIds = clickHouseService.getPersonIds(personId);
        List<LearningDTO> learningDTOS = clickHouseRepository.searchLearningResultsMAT(personIds, subjectName);
        List<LessonsDTO> lessons = new ArrayList<>();

        learningDTOS.stream().collect(Collectors.groupingBy(LearningDTO::getLessonLearningYear)).forEach((year, x) -> {
            LessonsDTO lesson = new LessonsDTO();
            lesson.setLearningYear(year);

            x.forEach(y -> {
                LessonsDTO.Topics topic = new LessonsDTO.Topics();
                topic.setLessonTopic(y.getLessonTheme());
                if (y.getLessonTheme().equals("Без темы")) {
                    topic.setLearningLessonNumber(y.getLearningLessonNumberEmptyTheme());
                    topic.setLearningPassedLessonNumber(y.getPassedLearningLessonNumberEmptyTheme());
                } else {
                    topic.setLearningLessonNumber(y.getLearningLessonNumber());
                    topic.setLearningPassedLessonNumber(y.getPassedLearningLessonNumber());
                }

                List<LessonDTO> lessonDTOS = clickHouseRepository.searchLessons(personIds, 3, y.getLessonId());
                Double average = null;
                if (!lessonDTOS.isEmpty()) {
                    average = lessonDTOS.stream().map(l -> l.getMarkValue5() * l.getMarkWeight()).mapToDouble(Integer::intValue).sum()
                            / lessonDTOS.stream().map(LessonDTO::getMarkWeight).mapToInt(Integer::intValue).sum();
                }
                topic.setAverageMark(Objects.nonNull(average) ? Precision.round(average, 2) : null);

                lesson.getTopics().add(topic);
            });


//            Map<String, List<LearningDTO>> themes = x.stream()
//                    .collect(Collectors.groupingBy(l -> Objects.isNull(l.getLessonTheme()) ? "Без темы" : l.getLessonTheme()));
//            themes.forEach((key, value) -> {
//                LessonsDTO.Topics topic = new LessonsDTO.Topics();
//                topic.setLessonTopic(key);
//                topic.setLearningLessonNumber(value.size());
//                topic.setLearningPassedLessonNumber((int) value.stream()
//                        .filter(l -> Objects.isNull(l.getLessonAttendingAttribute())).count());
//
//                List<LessonDTO> lessonDTOS = clickHouseRepository.searchLessons(personIds, 3,
//                        value.stream().map(LearningDTO::getLessonId).collect(Collectors.toList()));
//                Double average = null;
//                if (!lessonDTOS.isEmpty()) {
//                    average = lessonDTOS.stream().map(l -> l.getMarkValue5() * l.getMarkWeight()).mapToDouble(Integer::intValue).sum()
//                            / lessonDTOS.stream().map(LessonDTO::getMarkWeight).mapToInt(Integer::intValue).sum();
//                }
//                topic.setAverageMark(Objects.nonNull(average) ? Precision.round(average, 2) : null);
//
//                lesson.getTopics().add(topic);
//            });

            lesson.setNumberAllTopic(lesson.getTopics().size());
            lesson.setNumberDoneTopic(((int) lesson.getTopics().stream()
                    .filter(t -> t.getLearningLessonNumber().equals(t.getLearningPassedLessonNumber())).count()));
            lesson.setPercentDoneTopic(Double.valueOf(((double) lesson.getNumberDoneTopic() / lesson.getNumberAllTopic()) * 100).longValue());
            lessons.add(lesson);
        });

        return lessons;
    }

    @WriteOnly
    @Transactional
    public void deleteCheckInHistory(Long id) {
        CheckInHistory checkInHistory = checkInHistoryRepository.findById(id).orElseThrow(PortfolioException::get424);
        checkInHistory.setIsDelete(true);
        List<PersonAttachmentMetadata> attachments
                = personAttachmentMetadataRepository.findAllByEntityIdAndEntityType(checkInHistory.getId(), "checkIn");
        attachments.forEach(x -> x.setDeleted(true));
        personAttachmentMetadataRepository.saveAll(attachments);
        checkInHistoryRepository.save(checkInHistory);
    }


    private <E extends PersonallyEntity> Boolean checkCreatorId(E object, AccessTokenPayloadDto aupdToken) {
        String creatorId;

        PortfolioException.check(Objects.nonNull(object.getSource())
                        && !object.getSource().getCode().equals(15),
                PortfolioException.get567("Недостаточно прав для редактирования данных (Источник: Платформа Учи.Ру)"));

        if (isNull(aupdToken.getStf())) {
            User user = userRepository.findFirstByAupdId(aupdToken.getSub()).orElseThrow(PortfolioException::get424);
            CurrentUserRolesDTO currentUserRolesDTO = objectMapper.convertValue(user.getCurrentUserRoles(),
                    CurrentUserRolesDTO.class);
            String personId = object.getPersonId();

            if (currentUserRolesDTO.getCurrentMeshRoleId().equals(parentId)) {
                if (!user.getChildren().contains(personId)) {
                    throw PortfolioException.get562();
                }
            } else if (currentUserRolesDTO.getCurrentMeshRoleId().equals(childId)) {
                if (!personId.equals(aupdToken.getMsh())) {
                    throw PortfolioException.get562();
                }
            }
            // проверка на то, что запись создана родителем или учеником
            return 10 == object.getSource().getCode() || 11 == object.getSource().getCode();
        } else {
            creatorId = aupdToken.getStf();
        }
        return object.getCreatorId().equals(creatorId);
    }

    public List<Interest> saveInterestsList(List<Interest> interests) {
        List<Interest> toSave = new ArrayList<>();
        interests.forEach(x -> {
            toSave.add(crudService.convert(CastUtils.cast(x.reach(crudService)), Interest.class, null, true));
        });
        validateInterests(interests);
        return interestRepository.saveAll(toSave);
    }

    public void validateInterests(List<Interest> interests) {
        for (Interest interest : interests) {

            Integer headCode = interest.getInterestHead().getCode();
            String actionsString = interest.getInterestActionCode().stream().filter(Objects::nonNull).map(Objects::toString)
                    .collect(Collectors.joining(", "));
            // Проверка интереса из справочника, согласно InterestHead
            Class<InterestRefEntity> headInterest = InterestMap.getInterestByCode(headCode);
            // Если не нашелся класс интереса по основному коду - не валдиный интерес
            PortfolioException.check(Objects.nonNull(headInterest),
                    PortfolioException.get482(InterestHeadKindRef.class.getSimpleName()));
            InterestRefEntity mainInterestRef = null;
            try {
                mainInterestRef = crudService.findRef(interest.getInterestCode(), headInterest);
            } catch (Exception ex) {
                throw PortfolioException.get482(interest.getInterestCode().toString(), headInterest.getSimpleName());
            }
            // Если не нашелся код конкретного интереса - не валидный
            PortfolioException.check(Objects.nonNull(mainInterestRef),
                    PortfolioException.get482(interest.getInterestCode().toString(), headInterest.getSimpleName()));
            // Если категорая НЕ родительская - не валидный
            PortfolioException.check(Objects.isNull(mainInterestRef.getParentId()),
                    PortfolioException.get483(interest.getInterestCode().toString()));
            // Проверяем что действия переданные доступны для интереса
            PortfolioException.check(mainInterestRef.getInterestActionCode().containsAll(interest.getInterestActionCode()),
                    PortfolioException.get496(actionsString, interest.getInterestCode().toString()));


            // Проверка дополнительных параметров интереса из справочника, согласно InterestHead
            if (Objects.nonNull(interest.getSubinterestCode())) {
                interest.getSubinterestCode().forEach(subinterest -> {
                    InterestRefEntity subinterestRef = null;
                    try {
                        subinterestRef = crudService.findRef(subinterest, headInterest);
                    } catch (Exception ex) {
                        throw PortfolioException.get484(interest.getSubinterestCode().stream()
                                .filter(Objects::nonNull).map(Objects::toString).collect(Collectors.joining(", ")));
                    }
                    PortfolioException.check(Objects.nonNull(subinterestRef.getParentId()),
                            PortfolioException.get495(subinterest.toString(), interest.getInterestCode().toString()));
                    // Проверяем что действия переданные доступны для подинтереса
                    PortfolioException.check(subinterestRef.getInterestActionCode().containsAll(interest.getInterestActionCode()),
                            PortfolioException.get496(actionsString, interest.getInterestCode().toString()));

                });
            }
            // Заполнение действий интереса из справочника InterestAction
            List<InterestActionKindRef> allFoundActions =
                    actionKindRefRepository.findAllByCodeInAndHeadInterestCode(interest.getInterestActionCode(), headCode);
            PortfolioException.check(interest.getInterestActionCode().size() == allFoundActions.size(),
                    PortfolioException.get496(actionsString, interest.getInterestCode().toString()));

        }
    }

    @SuppressWarnings("all")
    public List<InterestDTO> getPersonInterests(String personId, String share) {
        if (Objects.nonNull(share)) {
            ShareLink shareLink = clickHouseService.parseCookie(share);
            PortfolioException.check(!(Objects.isNull(shareLink) || !shareLink.getInterests()), PortfolioException.get461());
        }
        List<Interest> interests = interestRepository.findAllByPersonId(personId);
        List<InterestDTO> interestDTO = mapToInterestDto(interests);
        fillInterestRefs(interestDTO);
        return interestDTO;
    }

    public void fillInterestRefs(List<InterestDTO> interestDTO) {
        interestDTO.forEach(interest -> {
            Integer headCode = interest.getInterestHead().getCode();
            // Заполнение интереса из справочника, согласно InterestHead
            Class<InterestRefEntity> interestByCode = InterestMap.getInterestByCode(headCode);
            Optional.ofNullable(interestByCode).ifPresent(x -> {
                interest.setInterest(crudService.findRef(interest.getInterestCode(), x));
            });
            // Заполнение дополнительных параметров интереса из справочника, согласно InterestHead
            interest.getSubinterestCode().forEach(subinterest -> {
                Class<InterestRefEntity> subinterestByCode = InterestMap.getInterestByCode(headCode);
                Optional.ofNullable(subinterestByCode).ifPresent(x -> {
                    interest.getSubinterest().add(crudService.findRef(subinterest, x));
                });
            });
            // Заполнение действий интереса из справочника InterestAction
            interest.getInterestActionCode().forEach(action -> {
                interest.getInterestAction()
                        .add(crudService.findRef(action, InterestActionKindRef.class));
            });
        });
    }

    public List<InterestDTO> mapToInterestDto(List<Interest> interests) {
        List<InterestDTO> result = new ArrayList<>();
        for (Interest interest : interests) {
            InterestDTO dto = new InterestDTO();
            BeanUtils.copyProperties(interest, dto);
            dto.setSource(interest.getSource());
            dto.setInterestHead(interest.getInterestHead());
            result.add(dto);
        }
        return result;
    }

    public void addErrorMessage(String bearer, String personId, ErrorMessageDTO errorMessageDTO) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        List<AggregatedGlobalRole> aggregatedGlobalRoles = authService.parseRLS(tokenPayload.getRls());
        PortfolioException.check(aggregatedGlobalRoles.stream()
                .anyMatch(x -> Arrays.asList(childId, parentId).contains(x.getId())), PortfolioException.get562());

        List<String> stopWords = stopWordRepository.findAll().stream().map(StopWordsRef::getWord).collect(Collectors.toList());
        Optional.ofNullable(errorMessageDTO.getErrorGeneralMessage())
                .ifPresent(x -> Utils.checkForSwear(x, stopWords));
        Optional.ofNullable(errorMessageDTO.getErrorChildEntityMessage())
                .ifPresent(x -> Utils.checkForSwear(x, stopWords));
        Optional.ofNullable(errorMessageDTO.getErrorFileMetadataMessage())
                .ifPresent(x -> Utils.checkForSwear(x, stopWords));

        List<AdministratorErrorRecord> existingRecords = administratorErrorRecordRepository
                .findByPersonIdAndErrorTypeCodeAndEntityIdAndEntityTypeAndRecordId(
                        personId, errorMessageDTO.getErrorTypeCode(), errorMessageDTO.getEntityId(),
                        errorMessageDTO.getEntityType(), errorMessageDTO.getRecordId());
        PortfolioException.check(existingRecords.isEmpty(), PortfolioException.get490());

        AdministratorErrorRecord aer = ErrorMessageDTO.mapAdminErrorRecordFromDto(errorMessageDTO, personId);
        aer.setCreatorId(tokenPayload.getMsh());
        DataSourceRef dataSource = crudService.findFirstRef(
                DataSourceRef.class, aggregatedGlobalRoles.stream().anyMatch(x -> childId.equals(x.getId())) ? 10 : 11);
        aer.setSourceCode(dataSource.getCode());
        administratorErrorRecordRepository.save(aer);
    }

    public List<ErrorMessageDTO> getErrorMessages(String entityType, Long entityId, String recordId) {
        List<AdministratorErrorRecord> allRecord = Collections.emptyList();

        if (Objects.isNull(entityId) == Objects.isNull(recordId)) {
            return Collections.emptyList();
        }
        if (Objects.nonNull(recordId)) {
            allRecord = administratorErrorRecordRepository.findAllByRecordId(recordId);
        }
        if (Objects.nonNull(entityId) && Objects.nonNull(entityType)) {
            allRecord = administratorErrorRecordRepository.findAllByEntityIdAndEntityType(entityId, entityType);
        } else if (Objects.nonNull(entityId)) {
            allRecord = administratorErrorRecordRepository.findAllByEntityId(entityId);
        }
        return allRecord.stream().map(ErrorMessageDTO::mapFromAdminErrorRecord).collect(Collectors.toList());
    }

    public ErrorMessageDTO updateErrorMessages(String bearer, String personId, ErrorMessageDTO errorMessageDTO) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        List<AggregatedGlobalRole> aggregatedGlobalRoles = authService.parseRLS(tokenPayload.getRls());
        PortfolioException.check(aggregatedGlobalRoles.stream()
                .anyMatch(x -> Arrays.asList(childId, parentId).contains(x.getId())), PortfolioException.get562());

        List<AdministratorErrorRecord> existingRecords = administratorErrorRecordRepository
                .findAllById(errorMessageDTO.getErrorId());
        PortfolioException.check(CollectionUtils.isNotEmpty(existingRecords), PortfolioException.get424());
        PortfolioException.check(existingRecords.size() == 1, PortfolioException.get491());

        List<String> stopWords = stopWordRepository.findAll().stream().map(StopWordsRef::getWord).collect(Collectors.toList());
        Optional.ofNullable(errorMessageDTO.getErrorGeneralMessage())
                .ifPresent(x -> Utils.checkForSwear(x, stopWords));
        Optional.ofNullable(errorMessageDTO.getErrorChildEntityMessage())
                .ifPresent(x -> Utils.checkForSwear(x, stopWords));
        Optional.ofNullable(errorMessageDTO.getErrorFileMetadataMessage())
                .ifPresent(x -> Utils.checkForSwear(x, stopWords));

        AdministratorErrorRecord errorRecord = existingRecords.get(0);
        ErrorMessageDTO.mapAdminErrorRecordFromDto(errorMessageDTO, errorRecord, personId);

        DataSourceRef dataSource = crudService.findFirstRef(
                DataSourceRef.class, aggregatedGlobalRoles.stream().anyMatch(x -> childId.equals(x.getId())) ? 10 : 11);
        errorRecord.setSourceCode(dataSource.getCode());
        errorRecord.setCreatorId(tokenPayload.getMsh());

        administratorErrorRecordRepository.save(errorRecord);
        return ErrorMessageDTO.mapFromAdminErrorRecord(errorRecord, errorMessageDTO);
    }

    @Transactional
    public GetRecommendationsByInterestResponse getRecommendationsByInterest(GetRecommendationsByInterestRequest request,
                                                                             String personId,
                                                                             String bearer) {
        PortfolioException.check(Lists.newArrayList(10, 11).contains(authService.getUserRoleFromToken(bearer)),
                PortfolioException.get562());
        List<String> nulls = new ArrayList<>();
        nulls.add(nonNull(personId) ? null : "personId");
        nulls.add(nonNull(request.getSchoolId()) ? null : "schoolId");
        nulls.add(nonNull(request.getClassLevel()) ? null : "classLevel");
        nulls = nulls.stream().filter(Objects::nonNull).collect(Collectors.toList());
        PortfolioException.check(nulls.isEmpty(), PortfolioException.get442(nulls.stream().collect(Collectors.joining(", "))));


        Integer classLevel;
        if (!StringUtils.isNumeric(request.getClassLevel())) {
            LearnerCategoryRef learnerCategoryRef = learnerCategoryRefRepository.findByValue(request.getClassLevel());
            if (Sets.newHashSet(19, 20).contains(learnerCategoryRef.getCode())) {
                classLevel = learnerCategoryRef.getCode().equals(19) ? 10 : 11;
            } else if (Sets.newHashSet(21, 22, 23).contains(learnerCategoryRef.getCode())) {
                return null;
            } else {
                throw PortfolioException.get464();
            }
        } else {
            classLevel = Integer.valueOf(request.getClassLevel());
        }
        // 5
        List<Interest> interests = interestRepository.findAllByPersonId(personId);
        if (interests.isEmpty()) return null;
        // 5.1
        List<OrganizationMetro> organizationMetro = organizationMetroRepository.findBySchoolId(request.getSchoolId().longValue());
        List<Integer> metroList = Utils.transform(organizationMetro, OrganizationMetro::getMetroCode);

        List<Integer> actionCodes = interests.stream()
                .map(Interest::getInterestActionCode)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());

        List<InterestActionKindRef> actionKinds = interestActionKindRefRepository.findAllById(actionCodes);
        Map<Integer, Set<GetRecommendationsByInterestResponse.RecommendInfo>> responseMap = new HashMap<>();
        String eszBearer = StringUtils.remove(bearer, "Bearer ");
        // 4.1
        boolean visitGTO = true;
        Set<Long> runnedClassificators = new HashSet<>();
        Set<Integer> runnedSectionCodes = new HashSet<>();

        // 4.2
        if (interests.stream().anyMatch(x -> x.getInterestHead().getCode().equals(1))
                && interests.stream().anyMatch(x -> x.getInterestActionCode().contains(1))) {
            List<SportReward> sportRewards = sportRewardRepository.findAllByPersonIdAndTypeCodeAndIsDelete(personId, 32, false);
            visitGTO = !sportRewards.isEmpty();
            // 4.3
            if (visitGTO) {
                visitGTO = checkRequirementsVisitGTO(personId, sportRewards);
                SportReward sportReward = sportRewards.stream().filter(x -> Objects.nonNull(x.getDate()))
                        .max(comparing(SportReward::getDate)).orElse(null);
                PersonDTO person = contingentService.getPerson(personId);
                if (Objects.isNull(sportReward) || Objects.isNull(person)
                        || Objects.isNull(sportReward.getDate()) || Objects.isNull(person.getBirthdate())) {
                    visitGTO = true;
                } else {
                    LocalDate birthdate = LocalDate.parse(person.getBirthdate());
                    long yearsPassed = ChronoUnit.YEARS.between(birthdate, sportReward.getDate());
                    Integer passedGTO = GTOYearsCategory.getBracket((int) yearsPassed);

                    long yearsNow = ChronoUnit.YEARS.between(birthdate, LocalDate.now());
                    Integer nowGTO = GTOYearsCategory.getBracket((int) yearsNow);
                    if (ObjectUtils.allNotNull(passedGTO, nowGTO)) {
                        visitGTO = passedGTO.equals(nowGTO);
                    } else {
                        visitGTO = true;
                    }
                }

            }
        }


        for (Interest interest : interests) {
//            if (interest.getInterestHead().getCode().equals(1) &&
//                    interest.getInterestActionCode().contains(1) && visitGTO) {
//                visitGTO = !sportRewardRepository.findAllByPersonIdAndTypeCode(personId, 32).isEmpty();
//            }

            List<Integer> sectionCodes = actionKinds.stream()
                    .filter(x -> interest.getInterestActionCode().contains(x.getCode()))
                    .map(x -> x.getSection().getCode())
                    .distinct()
                    .collect(Collectors.toList());
            runnedSectionCodes.addAll(sectionCodes);
            List<Long> classificatorIds = getClassificatorIds(interest);
            if (runnedClassificators.containsAll(classificatorIds) && runnedSectionCodes.containsAll(sectionCodes)) {
                continue;
            }
            runnedClassificators.addAll(classificatorIds);
            // 4.2
            CircleRequestDTO circleRequestDTO = new CircleRequestDTO();
            circleRequestDTO.setPersonId(personId);
            circleRequestDTO.setParallelId(classLevel);
            circleRequestDTO.setClassificatorIds(classificatorIds);
            circleRequestDTO.setMetroIds(metroList);
            SearchResultLimit searchResultLimit = new SearchResultLimit();
            searchResultLimit.setMaxValue(20);
            searchResultLimit.setMinValue(1);
            circleRequestDTO.setSearchResultLimit(searchResultLimit);
            try {
                CircleResponseDTO eszResponse = eszService.getCircle(eszBearer, circleRequestDTO);
                // 4.3
                String interestValue = getInterestRefValue(interest);
                List<CircleResponseDTO.SearchResItem> searchResItems = eszResponse.getSearchResItems().stream()
                        .filter(x -> x.getClassificatorName().equals(interestValue) || x.getDescription().equals(interestValue))
                        .collect(Collectors.toList());
                if (searchResItems.isEmpty() && !eszResponse.getSearchResItems().isEmpty()) {
                    List<String> headClassificators = classificatorIds.stream().map(x -> x.toString().substring(0, 3)).collect(Collectors.toList());
                    searchResItems = eszResponse.getSearchResItems().stream()
                            .filter(x -> headClassificators.contains(x.getClassificatorId().toString().substring(0, 3))).collect(Collectors.toList());
                }
                Set<GetRecommendationsByInterestResponse.RecommendInfo> recommendInfos = new HashSet<>();
                for (CircleResponseDTO.SearchResItem searchResItem : searchResItems) {
                    // 4.5
                    GetRecommendationsByInterestResponse.RecommendInfo recommendInfo =
                            new GetRecommendationsByInterestResponse.RecommendInfo();
                    recommendInfo.setPlaceServiceName(searchResItem.getPlaceService().getName());
                    recommendInfo.setProgrammShortName(searchResItem.getProgrammShortName());
                    recommendInfo.setServiceId(String.valueOf(searchResItem.getServiceId()));
                    recommendInfos.add(recommendInfo);
                }
                for (Integer sectionCode : sectionCodes) {
                    if (nonNull(responseMap.get(sectionCode))) {
                        responseMap.get(sectionCode).addAll(recommendInfos);
                    } else {
                        responseMap.put(sectionCode, recommendInfos);
                    }
                }
            } catch (Exception exception) {
                log.warn(exception.getMessage());
            }
        }
        GetRecommendationsByInterestResponse response = new GetRecommendationsByInterestResponse();
        for (Integer sectionCode : responseMap.keySet()) {
            GetRecommendationsByInterestResponse.RecommendObject recommendObject = new GetRecommendationsByInterestResponse.RecommendObject();
            recommendObject.setSectionCode(sectionCode);
            recommendObject.setRecommendInfo(responseMap.get(sectionCode));
            response.getRecommendObject().add(recommendObject);
        }

        // 8.1
        HashSet<Integer> codes = Sets.newHashSet(5, 13);
        if (interests.stream().map(Interest::getInterestHead)
                .map(InterestHeadKindRef::getCode)
                .anyMatch(codes::contains)) {

            // 8.2
            try {
                HttpServletRequest httpRequest = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
                Cookie aud = safetyGet(() ->
                        Utils.first(Arrays.asList(httpRequest.getCookies()), c -> c.getName().equals("aupd_current_role")));
                PortfolioException.check(Objects.nonNull(aud),
                        PortfolioException.get442("aupd_current_role"));
                LibraryResponseDTO materials = libraryService.getMaterials(bearer, classLevel, Utils.safeGet(aud, Cookie::getValue));
                // 8.3
                GetRecommendationsByInterestResponse.Literature literature = new GetRecommendationsByInterestResponse.Literature();
                materials.getLiteratureObject().forEach(x -> {
                    GetRecommendationsByInterestResponse.LiteratureObject object = new GetRecommendationsByInterestResponse.LiteratureObject();
                    object.setAuthor(x.getAuthor());
                    object.setClassLevelIds(x.getClass_level_ids());
                    object.setIconUrl(x.getIcon_url());
                    object.setLaunchUrl(x.getLaunch_url());
                    object.setName(x.getName());
                    object.setRating(x.getRating());
                    object.setSubjectIds(x.getSubject_ids());
                    object.setUserVotes(x.getUser_votes());
                    literature.getLiteratureObject().add(object);
                });
                literature.setCount(literature.getLiteratureObject().size());
                response.setLiterature(literature);
            } catch (Exception exception) {
                log.warn(exception.getMessage());
            }
        }
        response.setVisitGTO(visitGTO);
        return response;
    }

    private boolean checkRequirementsVisitGTO(String personId, List<SportReward> sportRewards) {
        SportReward sportReward = sportRewards.stream().filter(x -> Objects.nonNull(x.getDate()))
                .max(Comparator.comparing(SportReward::getDate)).orElse(null);
        PersonDTO person = contingentService.getPerson(personId);
        if (BooleanUtils.isFalse(Objects.nonNull(sportReward) && Objects.nonNull(sportReward.getDate())
                && Objects.nonNull(person) && Objects.nonNull(person.getBirthdate()))) {
            return true;
        }
//        PortfolioException.check(Objects.nonNull(sportReward.getDate()) && Objects.nonNull(person.getBirthdate()),
//                PortfolioException.get487("passed_gto"));
        LocalDate birthdate = LocalDate.parse(person.getBirthdate());
        long yearsPassed = ChronoUnit.YEARS.between(sportReward.getDate(), birthdate);
        Integer passedGTO = GTOYearsCategory.getBracket((int) yearsPassed);
        if (Objects.isNull(passedGTO)) {
            return true;
        }
//        PortfolioException.check(Objects.nonNull(passedGTO), PortfolioException.get487("passed_gto"));

        long yearsNow = ChronoUnit.YEARS.between(birthdate, LocalDate.now());
        Integer nowGTO = GTOYearsCategory.getBracket((int) yearsNow);
        if (Objects.nonNull(nowGTO)) {
            return passedGTO.equals(nowGTO);
        } else {
            return true;
        }
//        PortfolioException.check(Objects.nonNull(nowGTO), PortfolioException.get487("now_gto"));
    }

    private List<Long> getClassificatorIds(Interest interest) {
        List<Long> result = new ArrayList<>();
        if (interest.getInterestHead().getCode().equals(1)) {
            result.add(230000000L);
        } else if (interest.getInterestHead().getCode().equals(4) &&
                (interest.getInterestActionCode().contains(8) || interest.getInterestActionCode().contains(9))) {
            result.add(170000000L);
        } else if (interest.getInterestHead().getCode().equals(9)) {
            result.add(250000000L);
            result.add(300000000L);
            result.add(240000000L);
        } else if (interest.getInterestHead().getCode().equals(8)) {
            result.add(290000000L);
        } else if (interest.getInterestHead().getCode().equals(10) &&
                interest.getInterestActionCode().contains(25)) {
            result.add(240000000L);
            result.add(40000000L);
            result.add(90000000L);
            result.add(50000000L);
        } else if (interest.getInterestHead().getCode().equals(18)) {
            result.add(140000000L);
        } else if (interest.getInterestHead().getCode().equals(5) &&
                interest.getInterestActionCode().contains(13)) {
            result.add(150000000L);
        }
        return result;
    }

    private String getInterestRefValue(Interest interest) {
        Integer interestHeadCode = interest.getInterestHead().getCode();
        if (interestHeadCode.equals(1)) {
            SportKindRef ref = crudService.findRef(interest.getInterestCode(), SportKindRef.class);
            return ref.getValue();
        } else if (interestHeadCode.equals(4)) {
            InterestMusicKindRef ref = crudService.findRef(interest.getInterestCode(), InterestMusicKindRef.class);
            return ref.getValue();
        } else if (interestHeadCode.equals(9)) {
            InterestTheatricalKindRef ref = crudService.findRef(interest.getInterestCode(), InterestTheatricalKindRef.class);
            return ref.getValue();
        } else if (interestHeadCode.equals(8)) {
            InterestDanceKindRef ref = crudService.findRef(interest.getInterestCode(), InterestDanceKindRef.class);
            return ref.getValue();
        } else if (interestHeadCode.equals(10)) {
            InterestArtDesignKindRef ref = crudService.findRef(interest.getInterestCode(), InterestArtDesignKindRef.class);
            return ref.getValue();
        } else if (interestHeadCode.equals(18)) {
            InterestLanguageKindRef ref = crudService.findRef(interest.getInterestCode(), InterestLanguageKindRef.class);
            return ref.getValue();
        } else if (interestHeadCode.equals(5)) {
            InterestLiteratureKindRef ref = crudService.findRef(interest.getInterestCode(), InterestLiteratureKindRef.class);
            return ref.getValue();
        } else {
            return null;
        }
    }

    public List<ProffClassesRefDTO> getRecommendClasses(String personId, Integer schoolId, String classLevelStr, Integer limit) {
        if (!StringUtils.isNumeric(classLevelStr)) {
            return null;
        }
        Integer classLevel = Integer.valueOf(classLevelStr);
        if (classLevel < 5 || classLevel > 10) return null;
        String parallelRecommend;
        if (classLevel <= 7) {
            parallelRecommend = "7,8,9";
        } else {
            parallelRecommend = "10,11";
        }
        NsiDTO.Response nsiResponse = nsiService.getOrgName(schoolId).orElse(null);
        List<Interest> interests = interestRepository.findAllInterestRecommendClasses(personId);
        if (interests.isEmpty()) return null;
        List<ProffClassesRefDTO> response = new ArrayList<>();
        List<Integer> excludeCodes = new ArrayList<>();
        List<ProffClassesRef> proffClassesRefs =
                proffClassesRefRepository.findAllRecommendProfClasses(parallelRecommend, personId, schoolId);
        if (!proffClassesRefs.isEmpty()) {
            for (ProffClassesRef proffClassesRef : proffClassesRefs) {
                excludeCodes.add(proffClassesRef.getCode());
                ProffClassesRefDTO result = new ProffClassesRefDTO();
                result.setCode(proffClassesRef.getCode());
                result.setName(proffClassesRef.getValue());
                result.setLinkInfo(proffClassesRef.getLinkInfo1());
                result.setIsLocalSchool(true);
                response.add(result);
            }
        }
        Integer eoDistrictId;
        if (nonNull(nsiResponse)) {
            eoDistrictId = nsiResponse.getEoDistrictId().get(0).getKey();
        } else {
            List<School> schools = schoolRepository.findAllByNsiId(schoolId);
            if (schools.size() == 1) {
                eoDistrictId = schools.get(0).getDistrictId();
            } else {
                eoDistrictId = -1;
            }
        }
        List<ProffClassesRef> otherProffClassesRefs =
                proffClassesRefRepository.findAllRecommendClassesOtherSchool(parallelRecommend, personId, eoDistrictId, schoolId);
        otherProffClassesRefs.removeAll(proffClassesRefs);
        if (!otherProffClassesRefs.isEmpty()) {
            for (ProffClassesRef proffClassesRef : otherProffClassesRefs) {
                if (!excludeCodes.contains(proffClassesRef.getCode())) {
                    ProffClassesRefDTO result = new ProffClassesRefDTO();
                    result.setCode(proffClassesRef.getCode());
                    result.setName(proffClassesRef.getValue());
                    result.setLinkInfo(proffClassesRef.getLinkInfo2());
                    result.setIsLocalSchool(false);
                    response.add(result);
                }
            }
        }
        if (response.isEmpty()) return null;
        if (nonNull(limit) && limit < response.size()) {
            int x = response.size() - limit;
            for (int i = 0; i < x; i++) {
                response.remove(x + 1);
            }
        }
        return response;
    }

    @SuppressWarnings("all")
    public void checkStaffOOPermission(String bearer, String personId) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        List<AggregatedGlobalRole> aggregatedGlobalRoles = authService.parseRLS(tokenPayload.getRls());
        AggregatedGlobalRole aggregatedGlobalRole =
                aggregatedGlobalRoles.stream().filter(x -> employeeId.equals(x.getId())).findFirst().orElse(null);
        if (Objects.isNull(aggregatedGlobalRole)) {
            return;
        } else if (aggregatedGlobalRoles.size() > 1) {
            CurrentUserRolesDTO currentUserRolesGettingResponse = aupdService.getCurrentUserRoles(bearer, tokenPayload.getSub());
            PortfolioException.check(!BooleanUtils.isFalse(
                    currentUserRolesGettingResponse.getCurrentUserRolesGettingResult()), PortfolioException.get555());
            if (!currentUserRolesGettingResponse.getCurrentMeshRoleId().equals(employeeId)) {
                return;
            }
        }

        if (aggregatedGlobalRole.getLocalRoles().stream().noneMatch(x -> LongStream.range(32, 40)
                .boxed().collect(Collectors.toList()).contains(x.getId()))) {
            PersonDTO person = contingentService.getPersons(Collections.singletonList(personId))
                    .stream().findFirst().orElse(null);
            if (Objects.nonNull(person)) {
                EducationDTO educationDTO = person.getEducation().stream()
                        .filter(y -> nonNull(y.getParallelId()))
                        .max(comparing(EducationDTO::getParallelId)).orElse(null);
                if (aggregatedGlobalRole.getLocalRoles().stream()
                        .anyMatch(x -> Lists.newArrayList(adminOO, employeeOO).contains(x.getId()))) {
                    boolean sameOrganization = aggregatedGlobalRole.getLocalRoles().stream()
                            .filter(x -> Lists.newArrayList(employeeOO, adminOO).contains(x.getId()))
                            .map(AggregatedGlobalRole.AggregatedLocalRole::getOrgIds).flatMap(Collection::stream)
                            .collect(Collectors.toList()).contains(educationDTO.getOrganizationId());
                    PortfolioException.check(sameOrganization, PortfolioException.get421());
                } else if (aggregatedGlobalRole.getLocalRoles().stream().anyMatch(x -> teacherOO.equals(x.getId()))) {
                    PortfolioException.check(educationDTO.getStaffIds().contains(Integer.parseInt(tokenPayload.getStf())), PortfolioException.get421());
                }
            }
        }
    }

    public List<TeacherDTO> getTeachers(Integer orgId, String subject, Integer limit) {
        List<NsiDTO.Response> nsiData = nsiService.getTeachers(orgId, subject, limit);
        List<TeacherDTO> result = new ArrayList<>();
        nsiData.forEach(x -> {
            TeacherDTO dto = new TeacherDTO();
            dto.setFirstName(x.getFirstName());
            dto.setLastName(x.getSurname());
            dto.setPatronymic(x.getSecondName());
            dto.setStaffId(x.getGlobalId());
            result.add(dto);
        });
        return result;
    }

    public ProfessionalEducationResponse getProfessionalEducation(String bearer, String personId, String share) {
        ProfessionalEducationResponse response = new ProfessionalEducationResponse();
        if (nonNull(share)) {
            ShareLink shareLink = clickHouseService.parseCookie(share);
            PortfolioException.check(shareLink.getTrainingInfo(), PortfolioException.get461());
        } else {
            checkPermission(bearer, personId);
        }
        List<ProftechDTO> proftechData = clickHouseRepository.searchProfessionalEducation(personId);
        proftechData.sort(comparing(ProftechDTO::getChangeDateTime)
                .thenComparing(ProftechDTO::getEducationCourse).reversed());
        List<ProfessionalEducationResponse.ProfEducation> profEducations = new ArrayList<>();
        List<ProfessionalEducationResponse.Practice> practices = new ArrayList<>();

        fillProfessionalEducation(proftechData, profEducations, practices);

        response.setProfEducations(profEducations);
        response.setPractices(practices);
        return response;
    }

    public void fillProfessionalEducation(List<ProftechDTO> proftechData,
                                          List<ProfessionalEducationResponse.ProfEducation> profEducations,
                                          List<ProfessionalEducationResponse.Practice> practices) {
        proftechData.forEach(x -> {
            ProftechDTO proftechDTO = x;
            if (proftechData.stream().filter(y -> y.getEventId().equals(x.getEventId())).count() > 1) {
                if (profEducations.stream().anyMatch(y -> y.getEventId().equals(x.getEventId()))) {
                    return;
                }
                proftechDTO = proftechData.stream().filter(y -> y.getEventId().equals(x.getEventId())).findFirst().get();
            }

            ProfessionalEducationResponse.ProfEducation education =
                    Utils.copyNonNullPropertiesAndGet(proftechDTO, new ProfessionalEducationResponse.ProfEducation());
            profEducations.add(education);

            Optional.ofNullable(proftechDTO.getEGroupName()).ifPresent(education::setGroupName);
            Optional.ofNullable(proftechDTO.getEIsSmallGroup()).ifPresent(education::setIsSmallGroup);
            Optional.ofNullable(proftechDTO.getEIsOutletGroup()).ifPresent(education::setIsOutletGroup);

            if (Objects.nonNull(proftechDTO.getEStudyTerm())) {
                ProfessionalEducationResponse.StudyTerm studyTerm = new ProfessionalEducationResponse.StudyTerm();
                List<String> terms = Lists.newArrayList(proftechDTO.getEStudyTerm().split(" "));
                buildStudyTerm(education, studyTerm, terms);
                education.setIsEffectivePlan(true);
            } else if (Objects.nonNull(proftechDTO.getStudyTerm())) {
                ProfessionalEducationResponse.StudyTerm studyTerm = new ProfessionalEducationResponse.StudyTerm();
                List<String> terms = Lists.newArrayList(proftechDTO.getStudyTerm().split(" "));
                buildStudyTerm(education, studyTerm, terms);
                education.setIsEffectivePlan(false);
            }

            if (proftechDTO.getStatus().equals("STUDY")) {
                ProfessionalEducationResponse.PeriodHaveToStudy haveToStudy = new ProfessionalEducationResponse.PeriodHaveToStudy();
                LocalDate studyStartDate = proftechDTO.getStudyStartDate();
                LocalDate studyEndDate = studyStartDate
                        .plusYears(ObjectUtils.defaultIfNull(safetyGet(() -> education.getStudyTerm().getYears()), 0))
                        .plusMonths(ObjectUtils.defaultIfNull(safetyGet(() -> education.getStudyTerm().getMonths()), 0));
                if (Objects.nonNull(education.getStudyTerm()) && studyEndDate.isAfter(LocalDate.now())) {
                    Period between = Period.between(LocalDate.now(), studyEndDate);
                    haveToStudy.setDays(between.getDays());
                    haveToStudy.setMonths(between.getMonths());
                    haveToStudy.setYears(between.getYears());
                    education.setPeriodHaveToStudy(haveToStudy);
                }

            }

            if (BooleanUtils.isTrue(proftechDTO.getIsDocWithPracticeOrg())) {
                ProfessionalEducationResponse.Practice practice = new ProfessionalEducationResponse.Practice();
                practice.setRecordId(proftechDTO.getRecordId());
                practice.setCreationDate(proftechDTO.getCreationDate());
                if (Objects.nonNull(proftechDTO.getJobStudyPlace())) {
                    practice.setOrganization(proftechDTO.getJobStudyPlace());
                } else {
                    practice.setOrganization(proftechDTO.getOrganization());
                }
                practice.setIsDocOrganization(proftechDTO.getIsDocWithPracticeOrg());
                practice.setDocDate(proftechDTO.getDocIssueDate());
                practice.setDocNum(proftechDTO.getDocNum());
                practices.add(practice);
            }

        });
    }

    private void buildStudyTerm(ProfessionalEducationResponse.ProfEducation education,
                                ProfessionalEducationResponse.StudyTerm studyTerm, List<String> terms) {
        terms.forEach(t -> {
            if (t.contains("г")) {
                studyTerm.setYears(Integer.valueOf(t.replaceAll("\\D+", "")));
            } else if (t.contains("м")) {
                studyTerm.setMonths(Integer.valueOf(t.replaceAll("\\D+", "")));
            }
        });
        education.setStudyTerm(studyTerm);
    }

    public List<Document> getProfessionalEducationDocuments(String bearer, String personId, String share) {
        if (nonNull(share)) {
            ShareLink shareLink = clickHouseService.parseCookie(share);
            PortfolioException.check(shareLink.getDocuments(), PortfolioException.get461());
        } else {
            checkPermission(bearer, personId);
        }
        List<Document> documents = documentRepository.findAllByPersonId(personId);
        documents.forEach(x -> x.reachTransient(crudService));
        return documents;
    }

    public SpoStatusRef getProfessionalEducationSpoStatus(String bearer, String personId, String share) {
        if (nonNull(share)) {
            ShareLink shareLink = clickHouseService.parseCookie(share);
            PortfolioException.check(shareLink.getProfile(), PortfolioException.get461());
        } else {
            checkPermission(bearer, personId);
        }
        List<SpoStatus> spoStatuses = spoStatusRepository.findAllByPersonId(personId);
        if (spoStatuses.size() == 1) {
            return spoStatusRefRepository.findById(spoStatuses.get(0).getStatusCode()).orElse(null);
        } else {
            return null;
        }
    }

    public List<MetaskillRef> getProfessionalEducationMetaskill(String personId, String share) {
        if (Objects.nonNull(share)) {
            ShareLink shareLink = clickHouseService.parseCookie(share);
            PortfolioException.check(shareLink.getMetaskills(), PortfolioException.get461());
        }
        List<Metaskill> metaskills = metaskillRepository.findAllByPersonId(personId);
        if (metaskills.size() == 1) {
            return metaskillRefRepository.findAllById(metaskills.get(0).getMetaskillsCode());
        } else {
            return new ArrayList<>();
        }
    }

    public ProfessionalEducationJobResponse getProfessionalEducationJob(String bearer, String personId, String share) {
        ProfessionalEducationJobResponse response = new ProfessionalEducationJobResponse();
        if (nonNull(share)) {
            ShareLink shareLink = clickHouseService.parseCookie(share);
            PortfolioException.check(shareLink.getJob(), PortfolioException.get461());
        } else {
            checkPermission(bearer, personId);
        }
        // From ClickHouse
        List<ProftechJobDTO> proftechJobData = clickHouseRepository.searchProfessionalEducationJobByPersonId(personId);
        proftechJobData.sort(comparing(ProftechJobDTO::getChangeDateTime)
                .thenComparing(ProftechJobDTO::getCreationDate).reversed());
        List<String> eventIds = proftechJobData.stream()
                .map(ProftechJobDTO::getEventId)
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());

        List<ProfessionalEducationJobResponse.Job> jobs = new ArrayList<>();
        fillEducationJobListFromClick(jobs, proftechJobData, personId, eventIds);

        // From Postgres
        List<Job> jobsFromPostgres = jobRepository.findAllByPersonIdAndIsDelete(personId, false);
        fillEducationJobListFromPostgres(jobs, jobsFromPostgres);

        response.setJob(jobs);
        return response;
    }

    public void fillEducationJobListFromClick(List<ProfessionalEducationJobResponse.Job> jobs,
                                              List<ProftechJobDTO> proftechJobData, String personId,
                                              List<String> eventIds) {
        List<Job> jobsForClickhouse = jobRepository
                .findAllByPersonIdAndEventIdInAndIsDelete(personId, eventIds, false);
        proftechJobData.forEach(x -> {
            ProftechJobDTO proftechJobDTO = x;
            if (proftechJobData.stream().filter(y -> y.getEventId().equals(x.getEventId())).count() > 1) {
                if (jobs.stream().anyMatch(y -> y.getEventId().equals(x.getEventId()))) {
                    return;
                }
                proftechJobDTO = proftechJobData.stream().filter(y -> y.getEventId().equals(x.getEventId())).findFirst().get();
            }

            ProfessionalEducationJobResponse.Job job =
                    Utils.copyNonNullPropertiesAndGet(proftechJobDTO, new ProfessionalEducationJobResponse.Job());

            job.setEmploymentType(buildCodeValue(proftechJobDTO.getEmploymentTypeId(), proftechJobDTO.getEmploymentType()));
            job.setBusinessLevel(buildCodeValue(proftechJobDTO.getLevelBusinessId(), proftechJobDTO.getLevelBusiness()));
            job.setSalaryRange(buildCodeValue(proftechJobDTO.getSalaryRangeId(), proftechJobDTO.getSalaryRange()));

            ProfessionalEducationJobResponse.Contract contract = new ProfessionalEducationJobResponse.Contract();
            contract.setIsContract(proftechJobDTO.getIsDocForEmployment());
            contract.setDate(proftechJobDTO.getDocEmploymentDate());
            contract.setType(buildCodeValue(proftechJobDTO.getDocTypeId(), proftechJobDTO.getDocType()));
            job.setContract(contract);

            Job jobAdditional = jobsForClickhouse
                    .stream().filter(z -> z.getEventId().equals(job.getEventId())).findFirst().orElse(null);
            Optional.ofNullable(jobAdditional).ifPresent(z -> {
                job.setPosition(jobAdditional.getPosition());
                job.setMainFunctionality(jobAdditional.getMainFunctionality());
            });

            Optional.ofNullable(proftechJobDTO.getJobStudyPlace()).ifPresent(job::setOrganization);

            job.setSource(crudService.findFirstRef(DataSourceRef.class, 16));
            jobs.add(job);
        });
    }

    public void fillEducationJobListFromPostgres(List<ProfessionalEducationJobResponse.Job> jobs, List<Job> jobsFromPostgres) {
        jobsFromPostgres.forEach(x -> {
            ProfessionalEducationJobResponse.Job job =
                    Utils.copyNonNullPropertiesAndGet(x, new ProfessionalEducationJobResponse.Job());
            job.setId(x.getId());

            Optional.ofNullable(x.getBusinessLevel()).ifPresent(z ->
                    job.setBusinessLevel(buildCodeValue(z.getCode(), z.getValue())));
            Optional.ofNullable(x.getSalaryRange()).ifPresent(z ->
                    job.setSalaryRange(buildCodeValue(z.getCode(), z.getValue())));

            if (ObjectUtils.anyNotNull(x.getIsContract(), x.getContractDate(), x.getContractType())) {
                ProfessionalEducationJobResponse.Contract contract = new ProfessionalEducationJobResponse.Contract();
                contract.setIsContract(x.getIsContract());
                contract.setDate(x.getContractDate());
                Optional.ofNullable(x.getContractType()).ifPresent(z ->
                        contract.setType(buildCodeValue(z.getCode(), z.getValue())));
                job.setContract(contract);
            }
            //job.setSource(crudService.findFirstRef(DataSourceRef.class, 16));
            jobs.add(job);
        });
    }

    private void checkPermission(String bearer, String personId) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        HttpServletRequest httpRequest = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        Cookie aud = safetyGet(() ->
                Utils.first(Arrays.asList(httpRequest.getCookies()), c -> c.getName().equals("aupd_current_role")));
        PortfolioException.check(Objects.nonNull(aud), PortfolioException.get442("aupd_current_role"));
        Long globalRole = null;
        String aupdCurrentRole = (Utils.safeGet(aud, Cookie::getValue));
        if (nonNull(aupdCurrentRole)) {
            globalRole = Long.valueOf(aupdCurrentRole.split(":")[1]);
        }
        PortfolioException.check(nonNull(globalRole), PortfolioException.get421());
        boolean hasPermission = false;
        if (globalRole.equals(childId) || globalRole.equals(studentSpo)) {
            hasPermission =
                    nonNull(tokenPayload.getMsh()) && tokenPayload.getMsh().equals(personId);
        } else if (globalRole.equals(parentId)) {
            User user = userRepository.findFirstByAupdId(tokenPayload.getSub()).orElse(null);
            if (nonNull(user) && nonNull(user.getChildren()) && user.getChildren().contains(personId)) {
                hasPermission = true;
            }
        } else if (globalRole.equals(adminId)) {
            hasPermission = true;
        }
        PortfolioException.check(hasPermission, PortfolioException.get421());
    }

    private ProfessionalEducationJobResponse.CodeValue buildCodeValue(Integer code, String value) {
        ProfessionalEducationJobResponse.CodeValue codeValue = new ProfessionalEducationJobResponse.CodeValue();
        codeValue.setCode(code);
        codeValue.setValue(value);
        return codeValue;
    }

    public void updateMetaskill(String personId, Set<Integer> codes) {
        crudService.existAllRef(codes, MetaskillRef.class);
        Metaskill metaskill;
        List<Metaskill> metaskills = metaskillRepository.findAllByPersonId(personId);
        if (metaskills.size() == 0) {
            metaskill = new Metaskill();
            metaskill.setPersonId(personId);
        } else {
            metaskill = metaskills.get(0);
        }
        metaskill.setMetaskillsCode(new ArrayList<>(codes));
        metaskillRepository.save(metaskill);
    }

    public void updateSpoStatus(String personId, Integer code) {
        crudService.existAllRef(Collections.singleton(code), SpoStatusRef.class);
        SpoStatus spoStatus;
        List<SpoStatus> spoStatuses = spoStatusRepository.findAllByPersonId(personId);
        if (spoStatuses.size() == 0) {
            spoStatus = new SpoStatus();
            spoStatus.setPersonId(personId);
        } else {
            spoStatus = spoStatuses.get(0);
        }
        spoStatus.setStatusCode(code);
        spoStatusRepository.save(spoStatus);
    }

    public FavoriteUniversity addFavoriteUniversity(String bearer, String personId, Integer universityId) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        Integer sourceCode = authService.getUserRoleFromToken(bearer);
        PortfolioException.check(Lists.newArrayList(10, 11).contains(sourceCode),
                PortfolioException.get562());
        List<FavoriteUniversity> universities =
                favoriteUniversityRepository.findAllByPersonIdAndUniversityId(personId, universityId);
        Boolean existing = universities.stream().anyMatch(x -> !x.getIsDelete());
        PortfolioException.check(!existing, PortfolioException.get463());
        FavoriteUniversity universityByCreator = universities.stream()
                .filter(x -> x.getCreatorId().equals(tokenPayload.getMsh()))
                .findFirst().orElse(null);
        if (Objects.nonNull(universityByCreator)) {
            PortfolioException.check(universityByCreator.getIsDelete(),
                    PortfolioException.get463(existing.getClass().getSimpleName()));
            universityByCreator.setEditDate(LocalDateTime.now());
            universityByCreator.setIsDelete(false);
            favoriteUniversityRepository.save(universityByCreator);
        } else {
            FavoriteUniversity newFavorite = new FavoriteUniversity();
            newFavorite.setPersonId(personId);
            newFavorite.setUniversityId(universityId);
            newFavorite.setCreatorId(tokenPayload.getMsh());
            newFavorite.setSourceCode(sourceCode);
            newFavorite.setIsDelete(false);
            universityByCreator = favoriteUniversityRepository.save(newFavorite);
        }
        return universityByCreator;
    }

    public List<FavoriteUniversity> getFavoriteUniversities(String bearer, String personId) {
        return favoriteUniversityRepository.findAllByPersonIdAndIsDelete(personId, false);
    }

    public FavoriteUniversity deleteFavoriteUniversities(String bearer, String personId, Integer universityId) {
        AccessTokenPayloadDto tokenPayload = authService.getTokenPayload(bearer);
        PortfolioException.check(Lists.newArrayList(10, 11).contains(authService.getUserRoleFromToken(bearer)),
                PortfolioException.get562());
        FavoriteUniversity existing = favoriteUniversityRepository
                .findFirstByPersonIdAndUniversityIdAndIsDelete(personId, universityId, false)
                .orElseThrow(PortfolioException::get480);
        PortfolioException.check(existing.getCreatorId().equals(tokenPayload.getMsh()),
                PortfolioException.get562());

        existing.setEditDate(LocalDateTime.now());
        existing.setIsDelete(true);
        return favoriteUniversityRepository.save(existing);
    }

    public Cards getAllCards(String bearer, String personId) {
        Cards cards = new Cards();
        cards.setEvents(clickHouseService.getAchievementsFromClickhouse(
                personId, PersonallyEntityEnum.EVENT.getCode(), null));
        cards.setEmployments(clickHouseService.getAchievementsFromClickhouse(
                personId, PersonallyEntityEnum.EMPLOYMENT.getCode(), null));
        cards.setRewards(clickHouseService.getAchievementsFromClickhouse(
                personId, PersonallyEntityEnum.REWARD.getCode(), null));
        cards.setSportRewards(clickHouseService.getAchievementsFromClickhouse(
                personId, PersonallyEntityEnum.SPORT_REWARD.getCode(), null));
        cards.setAffilations(clickHouseService.getAchievementsFromClickhouse(
                personId, PersonallyEntityEnum.AFFILATION.getCode(), null));
        cards.setProjects(clickHouseService.getAchievementsFromClickhouse(
                personId, PersonallyEntityEnum.PROJECT.getCode(), null));
        cards.setWorldskills(clickHouseService.getAchievementsFromClickhouse(
                personId, PersonallyEntityEnum.GIA_WORLDSKILLS.getCode(), null));
        cards.setCertificates(getCertificates(personId, bearer, null).getCertificate());
        cards.setCheckIns(getCheckInHistoryByPersonId(personId, null));
        return cards;
    }

    public PersonalDataAccess getPersonalAccess(String bearer, String personId) {
        List<PersonalDataAccess> personalDatas = personalDataAccessRepository.findAllByPersonId(personId);
        if (personalDatas.isEmpty()) {
            PersonalDataAccess personalDataAccess = new PersonalDataAccess();
            personalDataAccess.setIsActive(false);
            personalDataAccess.setPersonId(personId);
            personalDataAccessRepository.save(personalDataAccess);
            return personalDataAccess;
        } else {
            PortfolioException.check(personalDatas.size() == 1, PortfolioException.get491());
            return personalDatas.iterator().next();
        }
    }

    public PersonalDataAccess changePersonalAccess(String bearer, String personId, Boolean isActive) {
        List<PersonalDataAccess> personalDataAccess = personalDataAccessRepository.findAllByPersonId(personId);
        PortfolioException.check(!personalDataAccess.isEmpty(), PortfolioException.get424());
        PortfolioException.check(personalDataAccess.size() == 1, PortfolioException.get491());
        PersonalDataAccess dataAccess = personalDataAccess.iterator().next();
        dataAccess.setIsActive(isActive);
        personalDataAccessRepository.save(dataAccess);
        return dataAccess;
    }

    public CardDTO getCard(String personId, Integer entityType, String entityId) {
        CardDTO response = new CardDTO();
        if (nonNull(Longs.tryParse(entityId))) {
            Long id = Long.valueOf(entityId);
            if (entityType.equals(1)) {
                Event event = eventRepository.findByIdAndPersonId(id, personId)
                        .orElseThrow(PortfolioException::get424);
                event.reachTransient(crudService);
                response.setCard(event);
                List<Object> mainRewards = rewardRepository.findAllByEntityId(entityId).stream()
                        .peek(x -> x.reachTransient(crudService)).collect(Collectors.toList());
                mainRewards.addAll(sportRewardRepository.findAllByEntityId(entityId).stream()
                        .peek(x -> x.reachTransient(crudService)).collect(Collectors.toList()));
                response.setMainReward(mainRewards);
            } else if (entityType.equals(2)) {
                Employment employment = employmentRepository.findByIdAndPersonId(id, personId)
                        .orElseThrow(PortfolioException::get424);
                employment.reachTransient(crudService);
                response.setCard(employment);
            } else if (entityType.equals(3)) {
                Reward reward = rewardRepository.findByIdAndPersonId(id, personId)
                        .orElseThrow(PortfolioException::get424);
                reward.reachTransient(crudService);
                if (Objects.nonNull(reward.getEntityId())) {
                    Optional<Event> event = eventRepository.findByIdAndPersonId(Long.valueOf(reward.getEntityId()), personId);
                    event.ifPresent(x -> {
                        x.reachTransient(crudService);
                        response.setEvent(x);
                    });
                }
                response.setCard(reward);
            } else if (entityType.equals(4)) {
                SportReward sportReward = sportRewardRepository.findByIdAndPersonId(id, personId)
                        .orElseThrow(PortfolioException::get424);
                sportReward.reachTransient(crudService);
                if (Objects.nonNull(sportReward.getEntityId())) {
                    Optional<Event> event = eventRepository.findByIdAndPersonId(Long.valueOf(sportReward.getEntityId()), personId);
                    event.ifPresent(x -> {
                        x.reachTransient(crudService);
                        response.setEvent(x);
                    });
                }
                response.setCard(sportReward);
            } else if (entityType.equals(5)) {
                Project project = projectRepository.findByIdAndPersonId(id, personId)
                        .orElseThrow(PortfolioException::get424);
                project.reachTransient(crudService);
                response.setCard(project);
                List<Object> mainRewards = rewardRepository.findAllByEntityId(entityId).stream()
                        .peek(x -> x.reachTransient(crudService)).collect(Collectors.toList());
                mainRewards.addAll(sportRewardRepository.findAllByEntityId(entityId).stream()
                        .peek(x -> x.reachTransient(crudService)).collect(Collectors.toList()));
                response.setMainReward(mainRewards);
            } else if (entityType.equals(6)) {
                Affilation affilation = affilationRepository.findByIdAndPersonId(id, personId)
                        .orElseThrow(PortfolioException::get424);
                affilation.reachTransient(crudService);
                response.setCard(affilation);
            } else if (entityType.equals(7)) {
                GIAWorldskills giaWorldskills = giaWorldskillsRepository.findByIdAndPersonId(id, personId)
                        .orElseThrow(PortfolioException::get424);
                response.setCard(giaWorldskills);
            } else if (entityType.equals(8)) {
                Job job = jobRepository.findByIdAndPersonId(id, personId)
                        .orElseThrow(PortfolioException::get424);
                response.setCard(job);
            } else if (entityType.equals(9)) {
                Document document = documentRepository.findByIdAndPersonId(id, personId)
                        .orElseThrow(PortfolioException::get424);
                response.setCard(document);
            } else if (entityType.equals(10)) {
                CheckInHistory checkInHistory = checkInHistoryRepository.findByIdAndPersonId(id, personId)
                        .orElseThrow(PortfolioException::get424);
                response.setCard(checkInHistory);
                UUID personIdUuid = UUID.fromString(personId);
                List<AttachmentProjection> checkIn = personAttachmentMetadataRepository.findAllByPersonIdAndEntityTypeAndDeleted(personIdUuid, "checkIn", false);
                response.setAttachments(checkIn);
            } else if (entityType.equals(11)) {
                GiaExam giaExam = giaExamRepository.findByIdAndPersonId(id, personId)
                        .orElseThrow(PortfolioException::get424);
                response.setCard(giaExam);
            }
            response.setLinkedObjects(getLinkedObjectList(id));
        } else {
            Collection<String> personIds = clickHouseService.getPersonIds(personId);
            List<String> recordIdList = Collections.singletonList(entityId);
            if (entityType.equals(2)) {
                List<AchievementClickDTO> clickDTOS =
                        clickHouseRepository.getAchievementByIds(personIds, 2, null, recordIdList);
                PortfolioException.check(!clickDTOS.isEmpty(), PortfolioException.get424());
                List<EmploymentDTO> resultList = clickHouseService.mapToEmployment(clickDTOS, personId);
                response.setCard(resultList.get(0));
            } else if (entityType.equals(3)) {
                List<AchievementClickDTO> clickDTOS =
                        clickHouseRepository.getAchievementByIds(personIds, 3, null, recordIdList);
                PortfolioException.check(!clickDTOS.isEmpty(), PortfolioException.get424());
                List<RewardDTO> resultList = clickHouseService.mapToReward(clickDTOS, personId);
                response.setCard(resultList.get(0));
            } else if (entityType.equals(4)) {
                List<AchievementClickDTO> clickDTOS =
                        clickHouseRepository.getAchievementByIds(personIds, 3, Collections.singletonList(1), recordIdList);
                PortfolioException.check(!clickDTOS.isEmpty(), PortfolioException.get424());
                List<SportRewardDTO> resultList = clickHouseService.mapToSportReward(clickDTOS, personId);
                response.setCard(resultList.get(0));
            } else if (entityType.equals(11) || entityType.equals(12)) { //todo simplify
                List<ProftechDTO> proftechDTOS =
                        clickHouseRepository.searchProfessionalEducationById(personId, entityId);
                PortfolioException.check(!proftechDTOS.isEmpty(), PortfolioException.get424());

                List<ProfessionalEducationResponse.ProfEducation> profEducations = new ArrayList<>();
                List<ProfessionalEducationResponse.Practice> practices = new ArrayList<>();
                fillProfessionalEducation(proftechDTOS, profEducations, practices);

                if (entityType.equals(11)) {
                    PortfolioException.check(!profEducations.isEmpty(), PortfolioException.get424());
                    response.setCard(profEducations.get(0));
                } else {
                    PortfolioException.check(!practices.isEmpty(), PortfolioException.get424());
                    response.setCard(practices.get(0));
                }
            } else if (entityType.equals(14)) {
                List<IndependentDiagnosticDTO> independentDiagnostics =
                        clickHouseRepository.searchIndependentDiagnosticById(personIds, recordIdList);
                PortfolioException.check(!independentDiagnostics.isEmpty(), PortfolioException.get424());
                response.setCard(independentDiagnostics.get(0));
            } else if (entityType.equals(15)) {
                List<SelfDiagnosticClickDTO> clickData =
                        clickHouseRepository.findSelfDiagnosticsByIds(personIds, recordIdList);
                PortfolioException.check(!clickData.isEmpty(), PortfolioException.get424());
                response.setCard(clickData.get(0));
            } else if (entityType.equals(16)) {
                List<CulturalInstitutionClickDTO> culturalInstitutions =
                        clickHouseRepository.findCulturalInstitutionsByIds(personId, recordIdList);
                PortfolioException.check(!culturalInstitutions.isEmpty(), PortfolioException.get424());
                response.setCard(culturalInstitutions.get(0));
            } else if (entityType.equals(17)) {
                List<CertificatesDTO.Certificate> certificates =
                        clickHouseRepository.searchCertificatesByIds(personIds, recordIdList);
                PortfolioException.check(!certificates.isEmpty(), PortfolioException.get424());
                response.setCard(certificates.get(0));
            }
        }
        return response;
    }


    private List<Object> getLinkedObjectList(Long entityId) {
        List<LinkedObject> linkedObjects = linkedObjectRepository.findAllByEntityId(entityId);
        if (linkedObjects.isEmpty()) return new ArrayList<>();
        Map<Integer, Set<Long>> linkedIdsMap = new HashMap<>();
        for (LinkedObject linkedObject : linkedObjects) {
            if (linkedObject.getEntityId1().equals(entityId)) {
                if (nonNull(linkedIdsMap.get(linkedObject.getEntityType2()))) {
                    linkedIdsMap.get(linkedObject.getEntityType2()).add(linkedObject.getEntityId2());
                } else {
                    linkedIdsMap.put(linkedObject.getEntityType2(), new HashSet<>());
                    linkedIdsMap.get(linkedObject.getEntityType2()).add(linkedObject.getEntityId2());
                }
            } else {
                if (nonNull(linkedIdsMap.get(linkedObject.getEntityType1()))) {
                    linkedIdsMap.get(linkedObject.getEntityType1()).add(linkedObject.getEntityId1());
                } else {
                    linkedIdsMap.put(linkedObject.getEntityType1(), new HashSet<>());
                    linkedIdsMap.get(linkedObject.getEntityType1()).add(linkedObject.getEntityId1());
                }
            }
        }
        List<Object> result = new ArrayList<>();
        for (Integer type : linkedIdsMap.keySet()) {
            Set<Long> ids = linkedIdsMap.get(type);
            if (type.equals(1)) {
                result.addAll(eventRepository.findAllById(ids));
            } else if (type.equals(2)) {
                result.addAll(employmentRepository.findAllById(ids));
            } else if (type.equals(3)) {
                result.addAll(rewardRepository.findAllById(ids));
            } else if (type.equals(4)) {
                result.addAll(sportRewardRepository.findAllById(ids));
            } else if (type.equals(5)) {
                result.addAll(projectRepository.findAllById(ids));
            } else if (type.equals(6)) {
                result.addAll(affilationRepository.findAllById(ids));
            } else if (type.equals(7)) {
                result.addAll(giaWorldskillsRepository.findAllById(ids));
            }
        }
        return result;
    }

    public AverageByThemeDTO getAverageMarkByTheme(String personId, Long subjectId) {
        // Ищем в контингенте учащегшося и его периоды обучения
        PersonDTO person = contingentService.getPerson(personId);
        List<EducationDTO> education = person.getEducation()
                .stream().filter(x -> x.getServiceTypeId().equals(2)).collect(Collectors.toList());

        // Ищем минимальную и максимальную дату обучения
        EducationDTO minEducation = education.stream().min(comparing(EducationDTO::getBegin)).orElse(null);
        EducationDTO maxEducation = education.stream().max(comparing(EducationDTO::getBegin)).orElse(null);

        // Если нет минимальной или максимальной (а значит никакой), возвращаем пустой ответ
        AverageByThemeDTO response = new AverageByThemeDTO();
        if (Objects.isNull(minEducation) || Objects.isNull(maxEducation)) {
            return response;
        }
        response.setSubjectId(subjectId);

        // Формируем периоды для ответа
        List<AverageByThemeDTO.Theme> themes = new ArrayList<>();
        IntStream.range(minEducation.getBegin().getYear(), maxEducation.getBegin().getYear() + 1).forEach(x -> {
            AverageByThemeDTO.Theme newPeriod = new AverageByThemeDTO.Theme();
            newPeriod.setLearningYear(x + "-" + (x + 1));
            themes.add(newPeriod);
        });

        // Формируем темы по периодам
        themes.forEach(themePeriod -> {
            List<PassedLesson> passedLessons = new ArrayList<>();
            List<EducationDTO> yearEducations = filterEducationByPeriod(education, themePeriod.getLearningYear());
            yearEducations.forEach(e -> {
                passedLessons.addAll(passedLessonRepository.findAllByParams(
                        e.getClassUid(), e.getBegin().atStartOfDay(), e.getEnd().atStartOfDay())
                        .stream().filter(x -> x.getSubjectId().equals(subjectId)).collect(Collectors.toList()));
            });
            if (passedLessons.isEmpty()) {
                return;
            }

            Map<Integer, List<PassedLesson>> groupedByTheme =
                    passedLessons.stream().collect(Collectors.groupingBy(PassedLesson::getLessonThemeId));

            groupedByTheme.forEach((t, passed) -> {
                List<MarkAverage> averageMarks = markAverageRepository.findAllByPersonIdAndThemeIdAndIsDelete(personId, t, false);
                PortfolioException.check(averageMarks.size() < 2, PortfolioException.get472());

                List<SkippedLesson> skipped = skippedLessonRepository.findAllByPersonIdAndLessonThemeIdAndIsDelete(personId, t, false);

                AverageByThemeDTO.AverageByTheme averageByTheme = new AverageByThemeDTO.AverageByTheme();
                averageByTheme.setThemeId(t);
                averageByTheme.setThemeName(passed.stream().map(PassedLesson::getLessonTheme).findFirst().orElse(null));

                averageByTheme.setLessonsTotalCount(passed.size());
                averageByTheme.setLessonsSkippedCount(skipped.size());
                averageByTheme.setLessonsPassedCount(passed.size() - skipped.size());

                if (!averageMarks.isEmpty()) {
                    MarkAverage markAverage = averageMarks.iterator().next();
                    averageByTheme.setValue(markAverage.getValue());
                    averageByTheme.setFivePointValue(markAverage.getFivePointValue());
                    averageByTheme.setGradeSystemType(AverageByThemeDTO.buildCodeValue(markAverage.getGradeSystemType()));

                    response.setSubjectName(markAverage.getSubjectName());
                }

                themePeriod.getAveragesByTheme().add(averageByTheme);
            });

            themePeriod.setThemeTotalCount(groupedByTheme.keySet().size());
            themePeriod.setThemePassedCount(Math.toIntExact(themePeriod.getAveragesByTheme().stream()
                    .filter(x -> x.getLessonsTotalCount().equals(x.getLessonsPassedCount())).count()));
            themePeriod.setPercentThemeLearned((themePeriod.getThemePassedCount() * 100F) / themePeriod.getThemeTotalCount());

            response.getTheme().add(themePeriod);
        });

        return response.getTheme().isEmpty() ? null : response;
    }

    private List<EducationDTO> filterEducationByPeriod(List<EducationDTO> educations, String period) {
        LocalDate begin = LocalDate.of(Integer.parseInt(period.split("-")[0]), 9, 1);
        LocalDate end = LocalDate.of(Integer.parseInt(period.split("-")[1]), 8, 31);
        return educations.stream().filter(x ->
                ((x.getBegin().isEqual(begin) || x.getBegin().isAfter(begin)) && x.getBegin().isBefore(end))
                        || (x.getEnd().isAfter(begin) && x.getEnd().isBefore(end))
                        || (x.getEnd().equals(LocalDate.of(2099, 1, 1))
                        && LocalDate.now().isAfter(begin) && LocalDate.now().isBefore(end))).collect(Collectors.toList());
    }

    public List<MarkDTO> getAverageMarks(String personId) {
        List<MarkAverage> allAverageMarks = markAverageRepository.findAllByPersonIdAndIsDelete(personId, false);

        if (allAverageMarks.isEmpty()) {
            return new ArrayList<>();
        }

        List<MarkDTO> response = new ArrayList<>();

        Map<String, List<MarkAverage>> groupedMarks = allAverageMarks.stream().collect(Collectors.groupingBy(MarkAverage::getYear));
        groupedMarks.keySet().forEach(k -> {
            Map<Integer, List<MarkAverage>> groupedByParallel =
                    groupedMarks.get(k).stream().collect(Collectors.groupingBy(MarkAverage::getParallelId));

            groupedByParallel.forEach((parallel, marks) -> {
                MarkDTO newMarkDTO = new MarkDTO();
                newMarkDTO.setYear(k);
                newMarkDTO.setEducationLevel(parallel <= 4 ? "НОО" : parallel <= 9 ? "ООО" : "СОО");

                newMarkDTO.setAverageAllSubjects(marks
                        .stream().filter(i -> i.getMarkType().getCode().equals(1))
                        .map(MarkAverage::getFivePointValue).mapToDouble(Double::valueOf).average()
                        .orElse(0));

                marks.stream().collect(Collectors.groupingBy(MarkAverage::getSubjectName))
                        .forEach((subject, subjectMarks) -> {
                            if (subjectMarks.isEmpty()) return;

                            MarkAverage finalBySubject = subjectMarks.stream().filter(x -> x.getMarkType().getCode().equals(2))
                                    .filter(x -> Objects.isNull(x.getAttestationPeriodId()))
                                    .max(comparing(MarkAverage::getCreationDate)).orElse(null);

                            MarkDTO.Subjects subjectDto = new MarkDTO.Subjects();

                            if (Objects.nonNull(finalBySubject)) {
                                subjectDto.setId(finalBySubject.getSubjectId());
                                subjectDto.setName(finalBySubject.getSubjectName());
                                subjectDto.setYearValue(finalBySubject.getValue());
                                subjectDto.setYearFivePointValue(finalBySubject.getFivePointValue());
                                subjectDto.setGradeSystemType(MarkDTO.buildCodeValue(finalBySubject.getGradeSystemType()));
                            } else {
                                subjectDto.setId(subjectMarks.stream().map(MarkAverage::getSubjectId).findFirst().orElse(null));
                                subjectDto.setName(subjectMarks.stream().map(MarkAverage::getSubjectName).findFirst().orElse(null));
                            }

                            subjectMarks.stream().filter(x -> x.getMarkType().getCode().equals(2))
                                    .filter(x -> Objects.nonNull(x.getAttestationPeriodId())).forEach(x -> {
                                MarkDTO.AttestationPeriod period = new MarkDTO.AttestationPeriod();
                                period.setId(x.getAttestationPeriodId());
                                period.setValue(x.getValue());
                                Optional.of(x.getFivePointValue()).ifPresent(f -> {
                                    period.setFivePointValue(BigDecimal.valueOf(f)
                                            .setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue());
                                });
                                period.setGradeSystemType(MarkDTO.buildCodeValue(x.getGradeSystemType()));
                                subjectDto.getAttestationPeriods().add(period);
                            });

                            newMarkDTO.getSubjects().add(subjectDto);
                        });
                if (Objects.isNull(newMarkDTO.getAverageAllSubjects())
                        || newMarkDTO.getAverageAllSubjects() == 0) {
                    newMarkDTO.setAverageAllSubjects(newMarkDTO.getSubjects().stream()
                            .peek(subject -> {
                                if (!subject.getAttestationPeriods().isEmpty()) {
                                    subject.setAttestationPeriods(
                                            Collections.singletonList(
                                                    Collections.max(subject.getAttestationPeriods(),
                                                            Comparator.comparingInt(MarkDTO.AttestationPeriod::getId))));
                                }
                            })
                            .map(MarkDTO.Subjects::getAttestationPeriods)
                            .flatMap(Collection::parallelStream)
                            .filter(Objects::nonNull)
                            .map(MarkDTO.AttestationPeriod::getFivePointValue)
                            .mapToDouble(Number::doubleValue).average().orElse(0));
                }
                newMarkDTO.setAverageAllSubjects(BigDecimal.valueOf(newMarkDTO.getAverageAllSubjects())
                        .setScale(2, RoundingMode.HALF_UP)
                        .doubleValue());
                response.add(newMarkDTO);
            });

        });
        return response;
    }

    public List<MarkDTO> getFinalMarks(String personId) {
        List<MarkFinal> allFinalMarks = markFinalRepository.findAllByPersonIdAndIsDelete(personId, false);
        if (allFinalMarks.isEmpty()) {
            return new ArrayList<>();
        }

        List<MarkDTO> response = new ArrayList<>();

        Map<String, List<MarkFinal>> groupedMarks = allFinalMarks.stream().collect(Collectors.groupingBy(MarkFinal::getYear));
        groupedMarks.keySet().forEach(k -> {
            Map<Integer, List<MarkFinal>> groupedByParallel =
                    groupedMarks.get(k).stream().collect(Collectors.groupingBy(MarkFinal::getParallelId));

            groupedByParallel.forEach((parallel, marks) -> {
                MarkDTO newMarkDTO = new MarkDTO();
                newMarkDTO.setYear(k);
                newMarkDTO.setEducationLevel(parallel <= 4 ? "НОО" : parallel <= 9 ? "ООО" : "СОО");

                newMarkDTO.setAverageAllSubjects(marks
                        .stream().filter(i -> i.getMarkType().getCode().equals(4))
                        .map(MarkFinal::getFivePointValue).mapToDouble(Double::valueOf).average()
                        .orElse(0));

                marks.stream().collect(Collectors.groupingBy(MarkFinal::getSubjectName))
                        .forEach((subject, subjectMarks) -> {
                            if (subjectMarks.isEmpty()) return;

                            MarkFinal finalBySubject = subjectMarks.stream().filter(x -> x.getMarkType().getCode().equals(4))
                                    .max(comparing(MarkFinal::getCreationDate)).orElse(null);

                            MarkDTO.Subjects subjectDto = new MarkDTO.Subjects();

                            if (Objects.nonNull(finalBySubject)) {
                                subjectDto.setId(finalBySubject.getSubjectId());
                                subjectDto.setName(finalBySubject.getSubjectName());
                                subjectDto.setYearValue(finalBySubject.getValue());
                                subjectDto.setYearFivePointValue(finalBySubject.getFivePointValue());
                                subjectDto.setGradeSystemType(MarkDTO.buildCodeValue(finalBySubject.getGradeSystemType()));
                            } else {
                                subjectDto.setId(subjectMarks.stream().map(MarkFinal::getSubjectId).findFirst().orElse(null));
                                subjectDto.setName(subjectMarks.stream().map(MarkFinal::getSubjectName).findFirst().orElse(null));
                            }

                            subjectMarks.stream().filter(x -> x.getMarkType().getCode().equals(3)).forEach(x -> {
                                MarkDTO.AttestationPeriod period = new MarkDTO.AttestationPeriod();
                                period.setId(x.getAttestationPeriodId());
                                period.setValue(x.getValue());
                                period.setFivePointValue(x.getFivePointValue());
                                period.setGradeSystemType(MarkDTO.buildCodeValue(x.getGradeSystemType()));
                                subjectDto.getAttestationPeriods().add(period);
                            });

                            newMarkDTO.getSubjects().add(subjectDto);
                        });

                response.add(newMarkDTO);
            });

        });
        return response;
    }
}


